{"workspaceFoldersFilePaths": ["c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main/"], "symbols": [{"name": "__param_position__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\config\\route.ts", "start": {"line": 39, "character": 8}, "end": {"line": 39, "character": 35}}, {"name": "__param_position__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\config\\route.ts", "start": {"line": 50, "character": 8}, "end": {"line": 50, "character": 36}}, {"name": "__param_position__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\config\\route.ts", "start": {"line": 78, "character": 8}, "end": {"line": 78, "character": 35}}, {"name": "__param_position__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\config\\route.ts", "start": {"line": 89, "character": 8}, "end": {"line": 89, "character": 36}}, {"name": "__param_position__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\config\\route.ts", "start": {"line": 117, "character": 8}, "end": {"line": 117, "character": 35}}, {"name": "__param_position__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\config\\route.ts", "start": {"line": 128, "character": 8}, "end": {"line": 128, "character": 36}}, {"name": "__param_position__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\config\\route.ts", "start": {"line": 156, "character": 8}, "end": {"line": 156, "character": 35}}, {"name": "__param_position__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\config\\route.ts", "start": {"line": 167, "character": 8}, "end": {"line": 167, "character": 36}}, {"name": "__param_position__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\config\\route.ts", "start": {"line": 195, "character": 8}, "end": {"line": 195, "character": 35}}, {"name": "__param_position__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\config\\route.ts", "start": {"line": 206, "character": 8}, "end": {"line": 206, "character": 36}}, {"name": "__param_position__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\config\\route.ts", "start": {"line": 234, "character": 8}, "end": {"line": 234, "character": 35}}, {"name": "__param_position__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\config\\route.ts", "start": {"line": 245, "character": 8}, "end": {"line": 245, "character": 36}}, {"name": "__param_position__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\config\\route.ts", "start": {"line": 273, "character": 8}, "end": {"line": 273, "character": 35}}, {"name": "__param_position__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\config\\route.ts", "start": {"line": 284, "character": 8}, "end": {"line": 284, "character": 36}}, {"name": "__param_position__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\config\\route.ts", "start": {"line": 336, "character": 2}, "end": {"line": 336, "character": 28}}, {"name": "__param_position__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\v1\\deployments\\config\\destinations\\route.ts", "start": {"line": 39, "character": 8}, "end": {"line": 39, "character": 35}}, {"name": "__param_position__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\v1\\deployments\\config\\destinations\\route.ts", "start": {"line": 50, "character": 8}, "end": {"line": 50, "character": 36}}, {"name": "__param_position__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\v1\\deployments\\config\\destinations\\route.ts", "start": {"line": 78, "character": 8}, "end": {"line": 78, "character": 35}}, {"name": "__param_position__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\v1\\deployments\\config\\destinations\\route.ts", "start": {"line": 89, "character": 8}, "end": {"line": 89, "character": 36}}, {"name": "__param_position__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\v1\\deployments\\config\\destinations\\route.ts", "start": {"line": 117, "character": 8}, "end": {"line": 117, "character": 35}}, {"name": "__param_position__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\v1\\deployments\\config\\destinations\\route.ts", "start": {"line": 128, "character": 8}, "end": {"line": 128, "character": 36}}, {"name": "__param_position__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\v1\\deployments\\config\\destinations\\route.ts", "start": {"line": 156, "character": 8}, "end": {"line": 156, "character": 35}}, {"name": "__param_position__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\v1\\deployments\\config\\destinations\\route.ts", "start": {"line": 167, "character": 8}, "end": {"line": 167, "character": 36}}, {"name": "__param_position__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\v1\\deployments\\config\\destinations\\route.ts", "start": {"line": 195, "character": 8}, "end": {"line": 195, "character": 35}}, {"name": "__param_position__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\v1\\deployments\\config\\destinations\\route.ts", "start": {"line": 206, "character": 8}, "end": {"line": 206, "character": 36}}, {"name": "__param_position__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\v1\\deployments\\config\\destinations\\route.ts", "start": {"line": 234, "character": 8}, "end": {"line": 234, "character": 35}}, {"name": "__param_position__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\v1\\deployments\\config\\destinations\\route.ts", "start": {"line": 245, "character": 8}, "end": {"line": 245, "character": 36}}, {"name": "__param_position__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\v1\\deployments\\config\\destinations\\route.ts", "start": {"line": 273, "character": 8}, "end": {"line": 273, "character": 35}}, {"name": "__param_position__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\v1\\deployments\\config\\destinations\\route.ts", "start": {"line": 284, "character": 8}, "end": {"line": 284, "character": 36}}, {"name": "__param_position__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\v1\\deployments\\config\\destinations\\route.ts", "start": {"line": 336, "character": 2}, "end": {"line": 336, "character": 28}}, {"name": "__param_position__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\v1\\machine-learning\\deployments\\config\\filter\\route.ts", "start": {"line": 39, "character": 8}, "end": {"line": 39, "character": 35}}, {"name": "__param_position__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\v1\\machine-learning\\deployments\\config\\filter\\route.ts", "start": {"line": 50, "character": 8}, "end": {"line": 50, "character": 36}}, {"name": "__param_position__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\v1\\machine-learning\\deployments\\config\\filter\\route.ts", "start": {"line": 78, "character": 8}, "end": {"line": 78, "character": 35}}, {"name": "__param_position__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\v1\\machine-learning\\deployments\\config\\filter\\route.ts", "start": {"line": 89, "character": 8}, "end": {"line": 89, "character": 36}}, {"name": "__param_position__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\v1\\machine-learning\\deployments\\config\\filter\\route.ts", "start": {"line": 117, "character": 8}, "end": {"line": 117, "character": 35}}, {"name": "__param_position__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\v1\\machine-learning\\deployments\\config\\filter\\route.ts", "start": {"line": 128, "character": 8}, "end": {"line": 128, "character": 36}}, {"name": "__param_position__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\v1\\machine-learning\\deployments\\config\\filter\\route.ts", "start": {"line": 156, "character": 8}, "end": {"line": 156, "character": 35}}, {"name": "__param_position__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\v1\\machine-learning\\deployments\\config\\filter\\route.ts", "start": {"line": 167, "character": 8}, "end": {"line": 167, "character": 36}}, {"name": "__param_position__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\v1\\machine-learning\\deployments\\config\\filter\\route.ts", "start": {"line": 195, "character": 8}, "end": {"line": 195, "character": 35}}, {"name": "__param_position__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\v1\\machine-learning\\deployments\\config\\filter\\route.ts", "start": {"line": 206, "character": 8}, "end": {"line": 206, "character": 36}}, {"name": "__param_position__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\v1\\machine-learning\\deployments\\config\\filter\\route.ts", "start": {"line": 234, "character": 8}, "end": {"line": 234, "character": 35}}, {"name": "__param_position__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\v1\\machine-learning\\deployments\\config\\filter\\route.ts", "start": {"line": 245, "character": 8}, "end": {"line": 245, "character": 36}}, {"name": "__param_position__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\v1\\machine-learning\\deployments\\config\\filter\\route.ts", "start": {"line": 273, "character": 8}, "end": {"line": 273, "character": 35}}, {"name": "__param_position__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\v1\\machine-learning\\deployments\\config\\filter\\route.ts", "start": {"line": 284, "character": 8}, "end": {"line": 284, "character": 36}}, {"name": "__param_position__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\v1\\machine-learning\\deployments\\config\\filter\\route.ts", "start": {"line": 336, "character": 2}, "end": {"line": 336, "character": 28}}, {"name": "__param_position__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\v1\\machine-learning\\deployments\\route.ts", "start": {"line": 39, "character": 8}, "end": {"line": 39, "character": 35}}, {"name": "__param_position__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\v1\\machine-learning\\deployments\\route.ts", "start": {"line": 50, "character": 8}, "end": {"line": 50, "character": 36}}, {"name": "__param_position__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\v1\\machine-learning\\deployments\\route.ts", "start": {"line": 78, "character": 8}, "end": {"line": 78, "character": 35}}, {"name": "__param_position__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\v1\\machine-learning\\deployments\\route.ts", "start": {"line": 89, "character": 8}, "end": {"line": 89, "character": 36}}, {"name": "__param_position__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\v1\\machine-learning\\deployments\\route.ts", "start": {"line": 117, "character": 8}, "end": {"line": 117, "character": 35}}, {"name": "__param_position__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\v1\\machine-learning\\deployments\\route.ts", "start": {"line": 128, "character": 8}, "end": {"line": 128, "character": 36}}, {"name": "__param_position__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\v1\\machine-learning\\deployments\\route.ts", "start": {"line": 156, "character": 8}, "end": {"line": 156, "character": 35}}, {"name": "__param_position__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\v1\\machine-learning\\deployments\\route.ts", "start": {"line": 167, "character": 8}, "end": {"line": 167, "character": 36}}, {"name": "__param_position__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\v1\\machine-learning\\deployments\\route.ts", "start": {"line": 195, "character": 8}, "end": {"line": 195, "character": 35}}, {"name": "__param_position__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\v1\\machine-learning\\deployments\\route.ts", "start": {"line": 206, "character": 8}, "end": {"line": 206, "character": 36}}, {"name": "__param_position__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\v1\\machine-learning\\deployments\\route.ts", "start": {"line": 234, "character": 8}, "end": {"line": 234, "character": 35}}, {"name": "__param_position__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\v1\\machine-learning\\deployments\\route.ts", "start": {"line": 245, "character": 8}, "end": {"line": 245, "character": 36}}, {"name": "__param_position__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\v1\\machine-learning\\deployments\\route.ts", "start": {"line": 273, "character": 8}, "end": {"line": 273, "character": 35}}, {"name": "__param_position__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\v1\\machine-learning\\deployments\\route.ts", "start": {"line": 284, "character": 8}, "end": {"line": 284, "character": 36}}, {"name": "__param_position__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\v1\\machine-learning\\deployments\\route.ts", "start": {"line": 336, "character": 2}, "end": {"line": 336, "character": 28}}, {"name": "__param_position__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\v1\\threats\\route.ts", "start": {"line": 39, "character": 8}, "end": {"line": 39, "character": 35}}, {"name": "__param_position__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\v1\\threats\\route.ts", "start": {"line": 50, "character": 8}, "end": {"line": 50, "character": 36}}, {"name": "__param_position__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\v1\\threats\\route.ts", "start": {"line": 78, "character": 8}, "end": {"line": 78, "character": 35}}, {"name": "__param_position__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\v1\\threats\\route.ts", "start": {"line": 89, "character": 8}, "end": {"line": 89, "character": 36}}, {"name": "__param_position__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\v1\\threats\\route.ts", "start": {"line": 117, "character": 8}, "end": {"line": 117, "character": 35}}, {"name": "__param_position__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\v1\\threats\\route.ts", "start": {"line": 128, "character": 8}, "end": {"line": 128, "character": 36}}, {"name": "__param_position__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\v1\\threats\\route.ts", "start": {"line": 156, "character": 8}, "end": {"line": 156, "character": 35}}, {"name": "__param_position__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\v1\\threats\\route.ts", "start": {"line": 167, "character": 8}, "end": {"line": 167, "character": 36}}, {"name": "__param_position__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\v1\\threats\\route.ts", "start": {"line": 195, "character": 8}, "end": {"line": 195, "character": 35}}, {"name": "__param_position__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\v1\\threats\\route.ts", "start": {"line": 206, "character": 8}, "end": {"line": 206, "character": 36}}, {"name": "__param_position__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\v1\\threats\\route.ts", "start": {"line": 234, "character": 8}, "end": {"line": 234, "character": 35}}, {"name": "__param_position__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\v1\\threats\\route.ts", "start": {"line": 245, "character": 8}, "end": {"line": 245, "character": 36}}, {"name": "__param_position__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\v1\\threats\\route.ts", "start": {"line": 273, "character": 8}, "end": {"line": 273, "character": 35}}, {"name": "__param_position__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\v1\\threats\\route.ts", "start": {"line": 284, "character": 8}, "end": {"line": 284, "character": 36}}, {"name": "__param_position__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\v1\\threats\\route.ts", "start": {"line": 336, "character": 2}, "end": {"line": 336, "character": 28}}, {"name": "__param_type__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\config\\route.ts", "start": {"line": 40, "character": 8}, "end": {"line": 40, "character": 59}}, {"name": "__param_type__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\config\\route.ts", "start": {"line": 51, "character": 8}, "end": {"line": 51, "character": 60}}, {"name": "__param_type__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\config\\route.ts", "start": {"line": 79, "character": 8}, "end": {"line": 79, "character": 60}}, {"name": "__param_type__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\config\\route.ts", "start": {"line": 90, "character": 8}, "end": {"line": 90, "character": 61}}, {"name": "__param_type__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\config\\route.ts", "start": {"line": 118, "character": 8}, "end": {"line": 118, "character": 63}}, {"name": "__param_type__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\config\\route.ts", "start": {"line": 129, "character": 8}, "end": {"line": 129, "character": 64}}, {"name": "__param_type__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\config\\route.ts", "start": {"line": 157, "character": 8}, "end": {"line": 157, "character": 60}}, {"name": "__param_type__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\config\\route.ts", "start": {"line": 168, "character": 8}, "end": {"line": 168, "character": 61}}, {"name": "__param_type__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\config\\route.ts", "start": {"line": 196, "character": 8}, "end": {"line": 196, "character": 59}}, {"name": "__param_type__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\config\\route.ts", "start": {"line": 207, "character": 8}, "end": {"line": 207, "character": 60}}, {"name": "__param_type__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\config\\route.ts", "start": {"line": 235, "character": 8}, "end": {"line": 235, "character": 62}}, {"name": "__param_type__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\config\\route.ts", "start": {"line": 246, "character": 8}, "end": {"line": 246, "character": 63}}, {"name": "__param_type__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\config\\route.ts", "start": {"line": 274, "character": 8}, "end": {"line": 274, "character": 61}}, {"name": "__param_type__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\config\\route.ts", "start": {"line": 285, "character": 8}, "end": {"line": 285, "character": 62}}, {"name": "__param_type__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\config\\route.ts", "start": {"line": 337, "character": 2}, "end": {"line": 337, "character": 19}}, {"name": "__param_type__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\v1\\deployments\\config\\destinations\\route.ts", "start": {"line": 40, "character": 8}, "end": {"line": 40, "character": 59}}, {"name": "__param_type__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\v1\\deployments\\config\\destinations\\route.ts", "start": {"line": 51, "character": 8}, "end": {"line": 51, "character": 60}}, {"name": "__param_type__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\v1\\deployments\\config\\destinations\\route.ts", "start": {"line": 79, "character": 8}, "end": {"line": 79, "character": 60}}, {"name": "__param_type__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\v1\\deployments\\config\\destinations\\route.ts", "start": {"line": 90, "character": 8}, "end": {"line": 90, "character": 61}}, {"name": "__param_type__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\v1\\deployments\\config\\destinations\\route.ts", "start": {"line": 118, "character": 8}, "end": {"line": 118, "character": 63}}, {"name": "__param_type__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\v1\\deployments\\config\\destinations\\route.ts", "start": {"line": 129, "character": 8}, "end": {"line": 129, "character": 64}}, {"name": "__param_type__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\v1\\deployments\\config\\destinations\\route.ts", "start": {"line": 157, "character": 8}, "end": {"line": 157, "character": 60}}, {"name": "__param_type__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\v1\\deployments\\config\\destinations\\route.ts", "start": {"line": 168, "character": 8}, "end": {"line": 168, "character": 61}}, {"name": "__param_type__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\v1\\deployments\\config\\destinations\\route.ts", "start": {"line": 196, "character": 8}, "end": {"line": 196, "character": 59}}, {"name": "__param_type__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\v1\\deployments\\config\\destinations\\route.ts", "start": {"line": 207, "character": 8}, "end": {"line": 207, "character": 60}}, {"name": "__param_type__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\v1\\deployments\\config\\destinations\\route.ts", "start": {"line": 235, "character": 8}, "end": {"line": 235, "character": 62}}, {"name": "__param_type__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\v1\\deployments\\config\\destinations\\route.ts", "start": {"line": 246, "character": 8}, "end": {"line": 246, "character": 63}}, {"name": "__param_type__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\v1\\deployments\\config\\destinations\\route.ts", "start": {"line": 274, "character": 8}, "end": {"line": 274, "character": 61}}, {"name": "__param_type__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\v1\\deployments\\config\\destinations\\route.ts", "start": {"line": 285, "character": 8}, "end": {"line": 285, "character": 62}}, {"name": "__param_type__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\v1\\deployments\\config\\destinations\\route.ts", "start": {"line": 337, "character": 2}, "end": {"line": 337, "character": 19}}, {"name": "__param_type__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\v1\\machine-learning\\deployments\\config\\filter\\route.ts", "start": {"line": 40, "character": 8}, "end": {"line": 40, "character": 59}}, {"name": "__param_type__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\v1\\machine-learning\\deployments\\config\\filter\\route.ts", "start": {"line": 51, "character": 8}, "end": {"line": 51, "character": 60}}, {"name": "__param_type__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\v1\\machine-learning\\deployments\\config\\filter\\route.ts", "start": {"line": 79, "character": 8}, "end": {"line": 79, "character": 60}}, {"name": "__param_type__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\v1\\machine-learning\\deployments\\config\\filter\\route.ts", "start": {"line": 90, "character": 8}, "end": {"line": 90, "character": 61}}, {"name": "__param_type__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\v1\\machine-learning\\deployments\\config\\filter\\route.ts", "start": {"line": 118, "character": 8}, "end": {"line": 118, "character": 63}}, {"name": "__param_type__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\v1\\machine-learning\\deployments\\config\\filter\\route.ts", "start": {"line": 129, "character": 8}, "end": {"line": 129, "character": 64}}, {"name": "__param_type__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\v1\\machine-learning\\deployments\\config\\filter\\route.ts", "start": {"line": 157, "character": 8}, "end": {"line": 157, "character": 60}}, {"name": "__param_type__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\v1\\machine-learning\\deployments\\config\\filter\\route.ts", "start": {"line": 168, "character": 8}, "end": {"line": 168, "character": 61}}, {"name": "__param_type__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\v1\\machine-learning\\deployments\\config\\filter\\route.ts", "start": {"line": 196, "character": 8}, "end": {"line": 196, "character": 59}}, {"name": "__param_type__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\v1\\machine-learning\\deployments\\config\\filter\\route.ts", "start": {"line": 207, "character": 8}, "end": {"line": 207, "character": 60}}, {"name": "__param_type__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\v1\\machine-learning\\deployments\\config\\filter\\route.ts", "start": {"line": 235, "character": 8}, "end": {"line": 235, "character": 62}}, {"name": "__param_type__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\v1\\machine-learning\\deployments\\config\\filter\\route.ts", "start": {"line": 246, "character": 8}, "end": {"line": 246, "character": 63}}, {"name": "__param_type__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\v1\\machine-learning\\deployments\\config\\filter\\route.ts", "start": {"line": 274, "character": 8}, "end": {"line": 274, "character": 61}}, {"name": "__param_type__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\v1\\machine-learning\\deployments\\config\\filter\\route.ts", "start": {"line": 285, "character": 8}, "end": {"line": 285, "character": 62}}, {"name": "__param_type__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\v1\\machine-learning\\deployments\\config\\filter\\route.ts", "start": {"line": 337, "character": 2}, "end": {"line": 337, "character": 19}}, {"name": "__param_type__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\v1\\machine-learning\\deployments\\route.ts", "start": {"line": 40, "character": 8}, "end": {"line": 40, "character": 59}}, {"name": "__param_type__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\v1\\machine-learning\\deployments\\route.ts", "start": {"line": 51, "character": 8}, "end": {"line": 51, "character": 60}}, {"name": "__param_type__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\v1\\machine-learning\\deployments\\route.ts", "start": {"line": 79, "character": 8}, "end": {"line": 79, "character": 60}}, {"name": "__param_type__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\v1\\machine-learning\\deployments\\route.ts", "start": {"line": 90, "character": 8}, "end": {"line": 90, "character": 61}}, {"name": "__param_type__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\v1\\machine-learning\\deployments\\route.ts", "start": {"line": 118, "character": 8}, "end": {"line": 118, "character": 63}}, {"name": "__param_type__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\v1\\machine-learning\\deployments\\route.ts", "start": {"line": 129, "character": 8}, "end": {"line": 129, "character": 64}}, {"name": "__param_type__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\v1\\machine-learning\\deployments\\route.ts", "start": {"line": 157, "character": 8}, "end": {"line": 157, "character": 60}}, {"name": "__param_type__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\v1\\machine-learning\\deployments\\route.ts", "start": {"line": 168, "character": 8}, "end": {"line": 168, "character": 61}}, {"name": "__param_type__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\v1\\machine-learning\\deployments\\route.ts", "start": {"line": 196, "character": 8}, "end": {"line": 196, "character": 59}}, {"name": "__param_type__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\v1\\machine-learning\\deployments\\route.ts", "start": {"line": 207, "character": 8}, "end": {"line": 207, "character": 60}}, {"name": "__param_type__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\v1\\machine-learning\\deployments\\route.ts", "start": {"line": 235, "character": 8}, "end": {"line": 235, "character": 62}}, {"name": "__param_type__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\v1\\machine-learning\\deployments\\route.ts", "start": {"line": 246, "character": 8}, "end": {"line": 246, "character": 63}}, {"name": "__param_type__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\v1\\machine-learning\\deployments\\route.ts", "start": {"line": 274, "character": 8}, "end": {"line": 274, "character": 61}}, {"name": "__param_type__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\v1\\machine-learning\\deployments\\route.ts", "start": {"line": 285, "character": 8}, "end": {"line": 285, "character": 62}}, {"name": "__param_type__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\v1\\machine-learning\\deployments\\route.ts", "start": {"line": 337, "character": 2}, "end": {"line": 337, "character": 19}}, {"name": "__param_type__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\v1\\threats\\route.ts", "start": {"line": 40, "character": 8}, "end": {"line": 40, "character": 59}}, {"name": "__param_type__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\v1\\threats\\route.ts", "start": {"line": 51, "character": 8}, "end": {"line": 51, "character": 60}}, {"name": "__param_type__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\v1\\threats\\route.ts", "start": {"line": 79, "character": 8}, "end": {"line": 79, "character": 60}}, {"name": "__param_type__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\v1\\threats\\route.ts", "start": {"line": 90, "character": 8}, "end": {"line": 90, "character": 61}}, {"name": "__param_type__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\v1\\threats\\route.ts", "start": {"line": 118, "character": 8}, "end": {"line": 118, "character": 63}}, {"name": "__param_type__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\v1\\threats\\route.ts", "start": {"line": 129, "character": 8}, "end": {"line": 129, "character": 64}}, {"name": "__param_type__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\v1\\threats\\route.ts", "start": {"line": 157, "character": 8}, "end": {"line": 157, "character": 60}}, {"name": "__param_type__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\v1\\threats\\route.ts", "start": {"line": 168, "character": 8}, "end": {"line": 168, "character": 61}}, {"name": "__param_type__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\v1\\threats\\route.ts", "start": {"line": 196, "character": 8}, "end": {"line": 196, "character": 59}}, {"name": "__param_type__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\v1\\threats\\route.ts", "start": {"line": 207, "character": 8}, "end": {"line": 207, "character": 60}}, {"name": "__param_type__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\v1\\threats\\route.ts", "start": {"line": 235, "character": 8}, "end": {"line": 235, "character": 62}}, {"name": "__param_type__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\v1\\threats\\route.ts", "start": {"line": 246, "character": 8}, "end": {"line": 246, "character": 63}}, {"name": "__param_type__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\v1\\threats\\route.ts", "start": {"line": 274, "character": 8}, "end": {"line": 274, "character": 61}}, {"name": "__param_type__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\v1\\threats\\route.ts", "start": {"line": 285, "character": 8}, "end": {"line": 285, "character": 62}}, {"name": "__param_type__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\v1\\threats\\route.ts", "start": {"line": 337, "character": 2}, "end": {"line": 337, "character": 19}}, {"name": "__return_type__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\config\\route.ts", "start": {"line": 61, "character": 8}, "end": {"line": 61, "character": 83}}, {"name": "__return_type__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\config\\route.ts", "start": {"line": 65, "character": 8}, "end": {"line": 65, "character": 62}}, {"name": "__return_type__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\config\\route.ts", "start": {"line": 100, "character": 8}, "end": {"line": 100, "character": 83}}, {"name": "__return_type__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\config\\route.ts", "start": {"line": 104, "character": 8}, "end": {"line": 104, "character": 63}}, {"name": "__return_type__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\config\\route.ts", "start": {"line": 139, "character": 8}, "end": {"line": 139, "character": 83}}, {"name": "__return_type__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\config\\route.ts", "start": {"line": 143, "character": 8}, "end": {"line": 143, "character": 66}}, {"name": "__return_type__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\config\\route.ts", "start": {"line": 178, "character": 8}, "end": {"line": 178, "character": 83}}, {"name": "__return_type__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\config\\route.ts", "start": {"line": 182, "character": 8}, "end": {"line": 182, "character": 63}}, {"name": "__return_type__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\config\\route.ts", "start": {"line": 217, "character": 8}, "end": {"line": 217, "character": 83}}, {"name": "__return_type__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\config\\route.ts", "start": {"line": 221, "character": 8}, "end": {"line": 221, "character": 62}}, {"name": "__return_type__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\config\\route.ts", "start": {"line": 256, "character": 8}, "end": {"line": 256, "character": 83}}, {"name": "__return_type__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\config\\route.ts", "start": {"line": 260, "character": 8}, "end": {"line": 260, "character": 65}}, {"name": "__return_type__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\config\\route.ts", "start": {"line": 295, "character": 8}, "end": {"line": 295, "character": 83}}, {"name": "__return_type__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\config\\route.ts", "start": {"line": 299, "character": 8}, "end": {"line": 299, "character": 64}}, {"name": "__return_type__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\config\\route.ts", "start": {"line": 309, "character": 54}, "end": {"line": 309, "character": 93}}, {"name": "__return_type__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\config\\route.ts", "start": {"line": 309, "character": 132}, "end": {"line": 309, "character": 203}}, {"name": "__return_type__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\v1\\deployments\\config\\destinations\\route.ts", "start": {"line": 61, "character": 8}, "end": {"line": 61, "character": 83}}, {"name": "__return_type__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\v1\\deployments\\config\\destinations\\route.ts", "start": {"line": 65, "character": 8}, "end": {"line": 65, "character": 62}}, {"name": "__return_type__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\v1\\deployments\\config\\destinations\\route.ts", "start": {"line": 100, "character": 8}, "end": {"line": 100, "character": 83}}, {"name": "__return_type__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\v1\\deployments\\config\\destinations\\route.ts", "start": {"line": 104, "character": 8}, "end": {"line": 104, "character": 63}}, {"name": "__return_type__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\v1\\deployments\\config\\destinations\\route.ts", "start": {"line": 139, "character": 8}, "end": {"line": 139, "character": 83}}, {"name": "__return_type__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\v1\\deployments\\config\\destinations\\route.ts", "start": {"line": 143, "character": 8}, "end": {"line": 143, "character": 66}}, {"name": "__return_type__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\v1\\deployments\\config\\destinations\\route.ts", "start": {"line": 178, "character": 8}, "end": {"line": 178, "character": 83}}, {"name": "__return_type__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\v1\\deployments\\config\\destinations\\route.ts", "start": {"line": 182, "character": 8}, "end": {"line": 182, "character": 63}}, {"name": "__return_type__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\v1\\deployments\\config\\destinations\\route.ts", "start": {"line": 217, "character": 8}, "end": {"line": 217, "character": 83}}, {"name": "__return_type__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\v1\\deployments\\config\\destinations\\route.ts", "start": {"line": 221, "character": 8}, "end": {"line": 221, "character": 62}}, {"name": "__return_type__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\v1\\deployments\\config\\destinations\\route.ts", "start": {"line": 256, "character": 8}, "end": {"line": 256, "character": 83}}, {"name": "__return_type__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\v1\\deployments\\config\\destinations\\route.ts", "start": {"line": 260, "character": 8}, "end": {"line": 260, "character": 65}}, {"name": "__return_type__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\v1\\deployments\\config\\destinations\\route.ts", "start": {"line": 295, "character": 8}, "end": {"line": 295, "character": 83}}, {"name": "__return_type__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\v1\\deployments\\config\\destinations\\route.ts", "start": {"line": 299, "character": 8}, "end": {"line": 299, "character": 64}}, {"name": "__return_type__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\v1\\deployments\\config\\destinations\\route.ts", "start": {"line": 309, "character": 54}, "end": {"line": 309, "character": 93}}, {"name": "__return_type__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\v1\\deployments\\config\\destinations\\route.ts", "start": {"line": 309, "character": 132}, "end": {"line": 309, "character": 203}}, {"name": "__return_type__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\v1\\machine-learning\\deployments\\config\\filter\\route.ts", "start": {"line": 61, "character": 8}, "end": {"line": 61, "character": 83}}, {"name": "__return_type__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\v1\\machine-learning\\deployments\\config\\filter\\route.ts", "start": {"line": 65, "character": 8}, "end": {"line": 65, "character": 62}}, {"name": "__return_type__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\v1\\machine-learning\\deployments\\config\\filter\\route.ts", "start": {"line": 100, "character": 8}, "end": {"line": 100, "character": 83}}, {"name": "__return_type__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\v1\\machine-learning\\deployments\\config\\filter\\route.ts", "start": {"line": 104, "character": 8}, "end": {"line": 104, "character": 63}}, {"name": "__return_type__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\v1\\machine-learning\\deployments\\config\\filter\\route.ts", "start": {"line": 139, "character": 8}, "end": {"line": 139, "character": 83}}, {"name": "__return_type__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\v1\\machine-learning\\deployments\\config\\filter\\route.ts", "start": {"line": 143, "character": 8}, "end": {"line": 143, "character": 66}}, {"name": "__return_type__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\v1\\machine-learning\\deployments\\config\\filter\\route.ts", "start": {"line": 178, "character": 8}, "end": {"line": 178, "character": 83}}, {"name": "__return_type__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\v1\\machine-learning\\deployments\\config\\filter\\route.ts", "start": {"line": 182, "character": 8}, "end": {"line": 182, "character": 63}}, {"name": "__return_type__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\v1\\machine-learning\\deployments\\config\\filter\\route.ts", "start": {"line": 217, "character": 8}, "end": {"line": 217, "character": 83}}, {"name": "__return_type__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\v1\\machine-learning\\deployments\\config\\filter\\route.ts", "start": {"line": 221, "character": 8}, "end": {"line": 221, "character": 62}}, {"name": "__return_type__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\v1\\machine-learning\\deployments\\config\\filter\\route.ts", "start": {"line": 256, "character": 8}, "end": {"line": 256, "character": 83}}, {"name": "__return_type__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\v1\\machine-learning\\deployments\\config\\filter\\route.ts", "start": {"line": 260, "character": 8}, "end": {"line": 260, "character": 65}}, {"name": "__return_type__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\v1\\machine-learning\\deployments\\config\\filter\\route.ts", "start": {"line": 295, "character": 8}, "end": {"line": 295, "character": 83}}, {"name": "__return_type__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\v1\\machine-learning\\deployments\\config\\filter\\route.ts", "start": {"line": 299, "character": 8}, "end": {"line": 299, "character": 64}}, {"name": "__return_type__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\v1\\machine-learning\\deployments\\config\\filter\\route.ts", "start": {"line": 309, "character": 54}, "end": {"line": 309, "character": 93}}, {"name": "__return_type__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\v1\\machine-learning\\deployments\\config\\filter\\route.ts", "start": {"line": 309, "character": 132}, "end": {"line": 309, "character": 203}}, {"name": "__return_type__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\v1\\machine-learning\\deployments\\route.ts", "start": {"line": 61, "character": 8}, "end": {"line": 61, "character": 83}}, {"name": "__return_type__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\v1\\machine-learning\\deployments\\route.ts", "start": {"line": 65, "character": 8}, "end": {"line": 65, "character": 62}}, {"name": "__return_type__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\v1\\machine-learning\\deployments\\route.ts", "start": {"line": 100, "character": 8}, "end": {"line": 100, "character": 83}}, {"name": "__return_type__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\v1\\machine-learning\\deployments\\route.ts", "start": {"line": 104, "character": 8}, "end": {"line": 104, "character": 63}}, {"name": "__return_type__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\v1\\machine-learning\\deployments\\route.ts", "start": {"line": 139, "character": 8}, "end": {"line": 139, "character": 83}}, {"name": "__return_type__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\v1\\machine-learning\\deployments\\route.ts", "start": {"line": 143, "character": 8}, "end": {"line": 143, "character": 66}}, {"name": "__return_type__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\v1\\machine-learning\\deployments\\route.ts", "start": {"line": 178, "character": 8}, "end": {"line": 178, "character": 83}}, {"name": "__return_type__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\v1\\machine-learning\\deployments\\route.ts", "start": {"line": 182, "character": 8}, "end": {"line": 182, "character": 63}}, {"name": "__return_type__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\v1\\machine-learning\\deployments\\route.ts", "start": {"line": 217, "character": 8}, "end": {"line": 217, "character": 83}}, {"name": "__return_type__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\v1\\machine-learning\\deployments\\route.ts", "start": {"line": 221, "character": 8}, "end": {"line": 221, "character": 62}}, {"name": "__return_type__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\v1\\machine-learning\\deployments\\route.ts", "start": {"line": 256, "character": 8}, "end": {"line": 256, "character": 83}}, {"name": "__return_type__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\v1\\machine-learning\\deployments\\route.ts", "start": {"line": 260, "character": 8}, "end": {"line": 260, "character": 65}}, {"name": "__return_type__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\v1\\machine-learning\\deployments\\route.ts", "start": {"line": 295, "character": 8}, "end": {"line": 295, "character": 83}}, {"name": "__return_type__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\v1\\machine-learning\\deployments\\route.ts", "start": {"line": 299, "character": 8}, "end": {"line": 299, "character": 64}}, {"name": "__return_type__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\v1\\machine-learning\\deployments\\route.ts", "start": {"line": 309, "character": 54}, "end": {"line": 309, "character": 93}}, {"name": "__return_type__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\v1\\machine-learning\\deployments\\route.ts", "start": {"line": 309, "character": 132}, "end": {"line": 309, "character": 203}}, {"name": "__return_type__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\v1\\threats\\route.ts", "start": {"line": 61, "character": 8}, "end": {"line": 61, "character": 83}}, {"name": "__return_type__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\v1\\threats\\route.ts", "start": {"line": 65, "character": 8}, "end": {"line": 65, "character": 62}}, {"name": "__return_type__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\v1\\threats\\route.ts", "start": {"line": 100, "character": 8}, "end": {"line": 100, "character": 83}}, {"name": "__return_type__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\v1\\threats\\route.ts", "start": {"line": 104, "character": 8}, "end": {"line": 104, "character": 63}}, {"name": "__return_type__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\v1\\threats\\route.ts", "start": {"line": 139, "character": 8}, "end": {"line": 139, "character": 83}}, {"name": "__return_type__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\v1\\threats\\route.ts", "start": {"line": 143, "character": 8}, "end": {"line": 143, "character": 66}}, {"name": "__return_type__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\v1\\threats\\route.ts", "start": {"line": 178, "character": 8}, "end": {"line": 178, "character": 83}}, {"name": "__return_type__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\v1\\threats\\route.ts", "start": {"line": 182, "character": 8}, "end": {"line": 182, "character": 63}}, {"name": "__return_type__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\v1\\threats\\route.ts", "start": {"line": 217, "character": 8}, "end": {"line": 217, "character": 83}}, {"name": "__return_type__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\v1\\threats\\route.ts", "start": {"line": 221, "character": 8}, "end": {"line": 221, "character": 62}}, {"name": "__return_type__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\v1\\threats\\route.ts", "start": {"line": 256, "character": 8}, "end": {"line": 256, "character": 83}}, {"name": "__return_type__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\v1\\threats\\route.ts", "start": {"line": 260, "character": 8}, "end": {"line": 260, "character": 65}}, {"name": "__return_type__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\v1\\threats\\route.ts", "start": {"line": 295, "character": 8}, "end": {"line": 295, "character": 83}}, {"name": "__return_type__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\v1\\threats\\route.ts", "start": {"line": 299, "character": 8}, "end": {"line": 299, "character": 64}}, {"name": "__return_type__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\v1\\threats\\route.ts", "start": {"line": 309, "character": 54}, "end": {"line": 309, "character": 93}}, {"name": "__return_type__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\v1\\threats\\route.ts", "start": {"line": 309, "character": 132}, "end": {"line": 309, "character": 203}}, {"name": "__return_type__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\layout.ts", "start": {"line": 50, "character": 54}, "end": {"line": 50, "character": 93}}, {"name": "__return_type__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\layout.ts", "start": {"line": 50, "character": 132}, "end": {"line": 50, "character": 203}}, {"name": "__return_type__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\login\\page.ts", "start": {"line": 50, "character": 54}, "end": {"line": 50, "character": 93}}, {"name": "__return_type__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\login\\page.ts", "start": {"line": 50, "character": 132}, "end": {"line": 50, "character": 203}}, {"name": "__return_type__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\page.ts", "start": {"line": 50, "character": 54}, "end": {"line": 50, "character": 93}}, {"name": "__return_type__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\page.ts", "start": {"line": 50, "character": 132}, "end": {"line": 50, "character": 203}}, {"name": "__return_type__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\settings\\page.ts", "start": {"line": 50, "character": 54}, "end": {"line": 50, "character": 93}}, {"name": "__return_type__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\settings\\page.ts", "start": {"line": 50, "character": 132}, "end": {"line": 50, "character": 203}}, {"name": "__tag__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\config\\route.ts", "start": {"line": 38, "character": 8}, "end": {"line": 38, "character": 22}}, {"name": "__tag__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\config\\route.ts", "start": {"line": 49, "character": 8}, "end": {"line": 49, "character": 22}}, {"name": "__tag__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\config\\route.ts", "start": {"line": 60, "character": 8}, "end": {"line": 60, "character": 23}}, {"name": "__tag__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\config\\route.ts", "start": {"line": 64, "character": 8}, "end": {"line": 64, "character": 23}}, {"name": "__tag__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\config\\route.ts", "start": {"line": 77, "character": 8}, "end": {"line": 77, "character": 23}}, {"name": "__tag__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\config\\route.ts", "start": {"line": 88, "character": 8}, "end": {"line": 88, "character": 23}}, {"name": "__tag__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\config\\route.ts", "start": {"line": 99, "character": 8}, "end": {"line": 99, "character": 24}}, {"name": "__tag__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\config\\route.ts", "start": {"line": 103, "character": 8}, "end": {"line": 103, "character": 24}}, {"name": "__tag__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\config\\route.ts", "start": {"line": 116, "character": 8}, "end": {"line": 116, "character": 26}}, {"name": "__tag__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\config\\route.ts", "start": {"line": 127, "character": 8}, "end": {"line": 127, "character": 26}}, {"name": "__tag__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\config\\route.ts", "start": {"line": 138, "character": 8}, "end": {"line": 138, "character": 27}}, {"name": "__tag__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\config\\route.ts", "start": {"line": 142, "character": 8}, "end": {"line": 142, "character": 27}}, {"name": "__tag__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\config\\route.ts", "start": {"line": 155, "character": 8}, "end": {"line": 155, "character": 23}}, {"name": "__tag__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\api\\config\\route.ts", "start": {"line": 166, "character": 8}, "end": {"line": 166, "character": 23}}, {"name": "__tag__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\layout.ts", "start": {"line": 50, "character": 21}, "end": {"line": 50, "character": 53}}, {"name": "__tag__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\layout.ts", "start": {"line": 50, "character": 99}, "end": {"line": 50, "character": 131}}, {"name": "__tag__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\page.ts", "start": {"line": 50, "character": 21}, "end": {"line": 50, "character": 53}}, {"name": "__tag__", "kind": 7, "containerName": "", "filePath": "c:\\Users\\<USER>\\Downloads\\intsoc-ui-main\\intsoc-ui-main\\apps\\frontend\\intsoc\\.next\\types\\app\\page.ts", "start": {"line": 50, "character": 99}, "end": {"line": 50, "character": 131}}], "activeFileDiagnostics": [], "debugConsoleOutput": "", "terminalBuffer": "\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\npanandhan@INDDT-R4J56E3:/mnt/c/Users/<USER>/Downloads/intsoc-ui-main/intsoc-ui-main$ panandhan@INDDT-R4J56E3:/mnt/c/Users/<USER>/Downloads/intsoc-ui-main/intsoc-ui-main$\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\npanandhan@INDDT-R4J56E3:/mnt/c/Users/<USER>/Downloads/intsoc-ui-main/intsoc-ui-main$panandhan@INDDT-R4J56E3:/mnt/c/Users/<USER>/Downloads/intsoc-ui-main/intsoc-ui-main$\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\npanandhan@INDDT-R4J56E3:/mnt/c/Users/<USER>/Downloads/intsoc-ui-main/intsoc-ui-main$panandhan@INDDT-R4J56E3:/mnt/c/Users/<USER>/Downloads/intsoc-ui-main/intsoc-ui-main$\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\npanandhan@INDDT-R4J56E3:/mnt/c/Users/<USER>/Downloads/intsoc-ui-main/intsoc-ui-main$panandhan@INDDT-R4J56E3:/mnt/c/Users/<USER>/Downloads/intsoc-ui-main/intsoc-ui-main$\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\rpanandhan@INDDT-R4J56E3:/mnt/c/Users/<USER>/Downloads/intsoc-ui-main/intsoc-ui-main$ panandhan@INDDT-R4J56E3:/mnt/c/Users/<USER>/Downloads/intsoc-ui-main/intsoc-ui-maipanandhan@INDDT-R4J56E3:/mnt/c/Users/<USER>/Downloads/intsoc-ui-main/intsoc-ui-main$\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\npanandhan@INDDT-R4J56E3:/mnt/c/Users/<USER>/Downloads/intsoc-ui-main/intsoc-ui-main$panandhan@INDDT-R4J56E3:/mnt/c/Users/<USER>/Downloads/intsoc-ui-main/intsoc-ui-panandhan@INDDT-R4J56E3:/mnt/c/Users/<USER>/Downloads/intsoc-ui-main/intsoc-ui-main$\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\npanandhan@INDDT-R4J56E3:/mnt/c/Users/<USER>/Downloads/intsoc-ui-main/intsoc-uipanandhan@INDDT-R4J56E3:/mnt/c/Users/<USER>/Downloads/intsoc-ui-main/intsoc-ui-main$\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\npanandhan@INDDT-R4J56E3:/mnt/c/Users/<USER>/Downloads/intsoc-ui-main/intsoc-panandhan@INDDT-R4J56E3:/mnt/c/Users/<USER>/Downloads/intsoc-ui-main/intsoc-ui-main$\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\npanandhan@INDDT-R4J56E3:/mnt/c/Users/<USER>/Downloads/intsoc-ui-main/intsopanandhan@INDDT-R4J56E3:/mnt/c/Users/<USER>/Downloads/intsoc-ui-main/intsoc-ui-main$\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\npanandhan@INDDT-R4J56E3:/mnt/c/Users/<USER>/Downloads/intsoc-ui-main/ipanandhan@INDDT-R4J56E3:/mnt/c/Users/<USER>/Downloads/intsoc-ui-main/intsoc-ui-main$\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\npanandhan@INDDT-R4J56E3:/mnt/c/Users/<USER>/Downloads/intsoc-ui-mainpanandhan@INDDT-R4J56E3:/mnt/c/Users/<USER>/Downloads/intsoc-ui-main/intsoc-ui-main$\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\npanandhan@INDDT-R4J56E3:/mnt/c/Users/<USER>/Downloads/intsoc-ui-maipanandhan@INDDT-R4J56E3:/mnt/c/Users/<USER>/Downloads/intsoc-ui-main/intsoc-ui-main$\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\npanandhan@INDDT-R4J56E3:/mnt/c/Users/<USER>/Downloads/intsoc-ui-mapanandhan@INDDT-R4J56E3:/mnt/c/Users/<USER>/Downloads/intsoc-ui-main/intsoc-ui-main$\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\npanandhan@INDDT-R4J56E3:/mnt/c/Users/<USER>/Downloads/intsoc-ui-panandhan@INDDT-R4J56E3:/mnt/c/Users/<USER>/Downloads/intsoc-ui-main/intsoc-ui-main$\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\npanandhan@INDDT-R4J56E3:/mnt/c/Users/<USER>/Downloads/intsoc-uipanandhan@INDDT-R4J56E3:/mnt/c/Users/<USER>/Downloads/intsoc-ui-main/intsoc-ui-main$\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\npanandhan@INDDT-R4J56E3:/mnt/c/Users/<USER>/Downloads/intsoc-panandhan@INDDT-R4J56E3:/mnt/c/Users/<USER>/Downloads/intsoc-ui-main/intsoc-ui-main$\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\npanandhan@INDDT-R4J56E3:/mnt/c/Users/<USER>/Downloads/intsocpanandhan@INDDT-R4J56E3:/mnt/c/Users/<USER>/Downloads/intsoc-ui-main/intsoc-ui-main$\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\npanandhan@INDDT-R4J56E3:/mnt/c/Users/<USER>/Downloads/intsopanandhan@INDDT-R4J56E3:/mnt/c/Users/<USER>/Downloads/intsoc-ui-main/intsoc-ui-main$\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\npanandhan@INDDT-R4J56E3:/mnt/c/Users/<USER>/Downloads/intspanandhan@INDDT-R4J56E3:/mnt/c/Users/<USER>/Downloads/intsoc-ui-main/intsoc-ui-main$\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\npanandhan@INDDT-R4J56E3:/mnt/c/Users/<USER>/Downloads/intpanandhan@INDDT-R4J56E3:/mnt/c/Users/<USER>/Downloads/intsoc-ui-main/intsoc-ui-main$\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\npanandhan@INDDT-R4J56E3:/mnt/c/Users/<USER>/Downloads/intpanandhan@INDDT-R4J56E3:/mnt/c/Users/<USER>/Downloads/intsoc-ui-main/intsoc-ui-main$\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\npanandhan@INDDT-R4J56E3:/mnt/c/Users/<USER>/Downloads/intsoc-ui-main/intsoc-ui-main$panandhan@INDDT-R4J56E3:/mnt/c/Users/<USER>/Downloads/inpanandhan@INDDT-R4J56E3:/mnt/c/Users/<USER>/Downloads/intsoc-ui-main/intsoc-ui-main$\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\npanandhan@INDDT-R4J56E3:/mnt/c/Users/<USER>/Downloads/intsoc-ui-main/intsoc-ui-main$panandhan@INDDT-R4J56E3:/mnt/c/Users/<USER>/Downloads/inpanandhpanandhan@INDDT-R4J56E3:/mnt/c/Users/<USER>/Downloads/intsoc-ui-main/intsoc-ui-main$\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\npanandhan@INDDT-R4J56E3:/mnt/c/Users/<USER>/Downloads/inpanandhppapanandhan@INDDT-R4J56E3:/mnt/c/Users/<USER>/Downloads/intsoc-ui-main/intsoc-ui-main$\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\npanandhan@INDDT-R4J56E3:/mnt/c/Users/<USER>/Downloads/inpanandhppappanandhan@INDDT-R4J56E3:/mnt/c/Users/<USER>/Downloads/intsoc-ui-main/intsoc-ui-main$\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\npanandhan@INDDT-R4J56E3:/mnt/c/Users/<USER>/Downloads/inpanandhppappapanandhan@INDDT-R4J56E3:/mnt/c/Users/<USER>/Downloads/intsoc-ui-main/intsoc-ui-main$\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\npanandhan@INDDT-R4J56E3:/mnt/c/Users/<USER>/Downloads/inpanandhppappapanandhan@INDDT-R4J56E3:/mnt/c/Users/<USER>/Downloads/intsoc-ui-main/intsoc-ui-main$\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\npanandhan@INDDT-R4J56E3:/mnt/c/Users/<USER>/Downloads/inpanandhppappapppanandhan@INDDT-R4J56E3:/mnt/c/Users/<USER>/Downloads/intsoc-ui-main/intsoc-ui-main$\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\npanandhan@INDDT-R4J56E3:/mnt/c/Users/<USER>/Downloads/inpanandhppappapppanandhan@INDDT-R4J56E3:/mnt/c/Users/<USER>/Downloads/intsoc-ui-main/intsoc-ui-main$\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\npanandhan@INDDT-R4J56E3:/mnt/c/Users/<USER>/Downloads/intsoc-ui-main/intsoc-ui-main$panandhan@INDDT-R4J56E3:/mnt/c/Users/<USER>/Downloads/inpanandhppappappppanandhan@INDDT-R4J56E3:/mnt/c/Users/<USER>/Downloads/intsoc-ui-main/intsoc-ui-main$\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\npanandhan@INDDT-R4J56E3:/mnt/c/Users/<USER>/Downloads/intsoc-ui-main/intsoc-ui-main$panandhan@INDDT-R4J56E3:/mnt/c/Users/<USER>/Downloads/inpanandhppappapppppanandhan@INDDT-R4J56E3:/mnt/c/Users/<USER>/Downloads/intsoc-ui-main/intsoc-ui-main$\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\npanandhan@INDDT-R4J56E3:/mnt/c/Users/<USER>/Downloads/intsoc-ui-main/intsoc-ui-main$panandhan@INDDT-R4J56E3:/mnt/c/Users/<USER>/Downloads/inpanandhppappapppppanandhan@INDDT-R4J56E3:/mnt/c/Users/<USER>/Downloads/intsoc-ui-main/intsoc-ui-main$\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\npanandhan@INDDT-R4J56E3:/mnt/c/Users/<USER>/Downloads/intsoc-ui-main/intsoc-ui-main$panandhan@INDDT-R4J56E3:/mnt/c/Users/<USER>/Downloads/inpanandhppappapppppanandhan@INDDT-R4J56E3:/mnt/c/Users/<USER>/Downloads/intsoc-ui-main/intsoc-ui-main$\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\npanandhan@INDDT-R4J56E3:/mnt/c/Users/<USER>/Downloads/inpanandhppappapppppanandhan@INDDT-R4J56E3:/mnt/c/Users/<USER>/Downloads/intsoc-ui-main/intsoc-ui-main$\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\npanandhan@INDDT-R4J56E3:/mnt/c/Users/<USER>/Downloads/inpanandhppappapppppanandhan@INDDT-R4J56E3:/mnt/c/Users/<USER>/Downloads/intsoc-ui-main/intsoc-ui-main$\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\npanandhan@INDDT-R4J56E3:/mnt/c/Users/<USER>/Downloads/inpanandhppappapppppanandhan@INDDT-R4J56E3:/mnt/c/Users/<USER>/Downloads/intsoc-ui-main/intsoc-ui-main$\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\npanandhan@INDDT-R4J56E3:/mnt/c/Users/<USER>/Downloads/inpanandhppappapppppanandhan@INDDT-R4J56E3:/mnt/c/Users/<USER>/Downloads/intsoc-ui-main/intsoc-ui-main$\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\npanandhan@INDDT-R4J56E3:/mnt/c/Users/<USER>/Downloads/inpanandhppappapppppanandhan@INDDT-R4J56E3:/mnt/c/Users/<USER>/Downloads/intsoc-ui-main/intsoc-ui-main$\r\n\r\n\r\n\r\n\r\n\r\npanandhan@INDDT-R4J56E3:/mnt/c/Users/<USER>/Downloads/inpanandhppappapppppanandhan@INDDT-R4J56E3:/mnt/c/Users/<USER>/Downloads/intsoc-ui-main/intsoc-ui-main$\r\n\r\n\r\n\r\n\r\npanandhan@INDDT-R4J56E3:/mnt/c/Users/<USER>/Downloads/inpanandhppappapppppanandhan@INDDT-R4J56E3:/mnt/c/Users/<USER>/Downloads/intsoc-ui-main/intsoc-ui-main$\r\n\r\n\r\n\r\npanandhan@INDDT-R4J56E3:/mnt/c/Users/<USER>/Downloads/inpanandhppappapppppanandhan@INDDT-R4J56E3:/mnt/c/Users/<USER>/Downloads/intsoc-ui-main/intsoc-ui-main$\r\n\r\n\r\npanandhan@INDDT-R4J56E3:/mnt/c/Users/<USER>/Downloads/inpanandhppappapppppanandhan@INDDT-R4J56E3:/mnt/c/Users/<USER>/Downloads/intsoc-ui-main/intsoc-ui-main$\r\n\r\npanandhan@INDDT-R4J56E3:/mnt/c/Users/<USER>/Downloads/inpanandhppappapppppanandhan@INDDT-R4J56E3:/mnt/c/Users/<USER>/Downloads/intsoc-ui-main/intsoc-ui-main$\r\npanandhan@INDDT-R4J56E3:/mnt/c/Users/<USER>/Downloads/intsoc-ui-main/intsoc-ui-main$panandhan@INDDT-R4J56E3:/mnt/c/Users/<USER>/Downloads/inpanandhppappapppppanandhan@INDDT-R4J56E3:/mnt/c/Users/<USER>/Downloads/intsoc-ui-main/intsoc-ui-main$panandhan@INDDT-R4J56E3:/mnt/c/Users/<USER>/Downloads/intsoc-ui-main/intsoc-ui-main$panandhan@INDDT-R4J56E3:/mnt/c/Users/<USER>/Downloads/intsoc-ui-main/intsoc-ui-main$", "terminalSelection": "", "terminalShellType": "wsl", "repoContexts": [], "notebookDocumentFilePaths": [], "textDocumentFilePaths": ["apps/frontend/intsoc/src/app/settings/page.tsx"]}
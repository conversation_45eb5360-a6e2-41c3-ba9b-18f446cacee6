'use client';
 
import { <PERSON>, CardHeader, CardContent } from '@telesoft/ui';
import { ThreatFlowDiagram } from '@telesoft/d3';
import { useState, useMemo, useEffect } from 'react';
import { classNames, STYLE_PRESETS } from '@telesoft/utils';
import { useThreatHunting } from '../../lib/hooks/useThreatHunting';
 
export default function ThreatHunting() {
  const { threatHunts, sourceTypeCounts, loading, error, refresh } = useThreatHunting();
  const [sourceAActive, setSourceAActive] = useState(false);
  const [sourceBActive, setSourceBActive] = useState(false);
  const [processedActive, setProcessedActive] = useState(false);
 
  // Calculate real metrics from threat hunts data
  const activeHunts = useMemo(() => {
    if (!threatHunts.length) {
      return { running: 0, queued: 0, completed: 0 };
    }
 
    const running = threatHunts.filter(hunt => hunt.hunt_status === 'running').length;
    const queued = threatHunts.filter(hunt => hunt.hunt_status === 'queued').length;
    const completed = threatHunts.filter(hunt => hunt.hunt_status === 'complete').length;
 
    return { running, queued, completed };
  }, [threatHunts]);
  const [detectionRate, setDetectionRate] = useState(94.7);
  const [systemHealth, setSystemHealth] = useState({
    processingLoad: 67,
    memoryUsage: 42,
  });
 
  // Continuous animation cycle - keep everything active with minor metric updates
  useEffect(() => {
    // Set everything to active immediately for constant "analyzing" state
    setSourceAActive(true);
    setSourceBActive(true);
    setProcessedActive(true);
 
    const updateMetrics = () => {
      // Keep the existing animation logic for other metrics
      // activeHunts is now calculated from real data
 
      setDetectionRate((prev) =>
        Math.max(92, Math.min(98, prev + (Math.random() - 0.5) * 0.2)),
      );
 
      setSystemHealth((prev) => ({
        processingLoad: Math.max(
          45,
          Math.min(85, prev.processingLoad + (Math.random() - 0.5) * 3),
        ),
        memoryUsage: Math.max(
          35,
          Math.min(70, prev.memoryUsage + (Math.random() - 0.5) * 2),
        ),
      }));
    };
 
    // Initial update
    updateMetrics();
 
    // Set up continuous metric updates
    const metricsInterval = setInterval(
      () => {
        updateMetrics();
      },
      2000 + Math.random() * 2000,
    ); // Update every 2-4 seconds
 
    return () => {
      clearInterval(metricsInterval);
    };
  }, []);
 
  // D3 Flow Diagram Data - using real source types from API
  const flowNodes = useMemo(
    () => {
      // Create source nodes from ALL real data (remove slice limit)
      const sourceNodes = sourceTypeCounts.map((sourceType, index) => ({
        id: `source-${sourceType.source_type}`,
        label: sourceType.source_type.charAt(0).toUpperCase() + sourceType.source_type.slice(1),
        description: `${sourceType.count} hunts`,
        type: 'source' as const,
        source_type: sourceType.source_type,
        active: sourceAActive || sourceBActive, // Make all sources active
      }));
 
      // If no real data, fall back to default sources
      const defaultSourceNodes = [
        {
          id: 'threat-intelligence',
          label: 'Threat Intelligence',
          description: 'CTX, MISP & IOC Feeds',
          type: 'source' as const,
          source_type: 'threat_intelligence',
          active: sourceAActive,
        },
        {
          id: 'news',
          label: 'News',
          description: 'Threat Summaries & Intelligence',
          type: 'source' as const,
          source_type: 'news',
          active: sourceBActive,
        },
        {
          id: 'playbook',
          label: 'Playbook',
          description: 'Security Policies & Rules',
          type: 'source' as const,
          source_type: 'playbook',
          active: sourceBActive,
        },
      ];
 
      const finalSourceNodes = sourceNodes.length > 0 ? sourceNodes : defaultSourceNodes;
 
      return [
        ...finalSourceNodes,
        // Central Processor
        {
          id: 'processor',
          label: 'Correlation Engine',
          description: 'Advanced Analytics',
          type: 'processor' as const,
          metrics: [
            {
              label: 'Correlation Rate',
              value: processedActive
                ? `${(97.5 + Math.random() * 2).toFixed(1)}%`
                : '0%',
              status: 'good' as const,
            },
            {
              label: 'Active Rules',
              value: processedActive
                ? Math.floor(150 + Math.random() * 15).toString()
                : '0',
              status: 'good' as const,
            },
          ],
          active: processedActive,
        },
        // Outputs
        {
          id: 'output-conclusive',
          label: 'Conclusive',
          description: processedActive
            ? Math.floor(8 + Math.random() * 8).toString()
            : '0',
          type: 'output' as const,
          metrics: [],
          active: processedActive,
        },
        {
          id: 'output-inconclusive',
          label: 'Inconclusive',
          description: processedActive
            ? Math.floor(520 + Math.random() * 80).toString()
            : '0',
          type: 'output' as const,
          metrics: [],
          active: processedActive,
        },
      ];
    },
    [sourceAActive, sourceBActive, processedActive, sourceTypeCounts],
  );
 
  const flowLinks = useMemo(
    () => {
      // Create links from ALL source nodes to processor (remove slice limit)
      const sourceLinks = sourceTypeCounts.map((sourceType) => ({
        source: `source-${sourceType.source_type}`,
        target: 'processor',
        active: sourceAActive || sourceBActive,
      }));
 
      // If no real data, use default links
      const defaultLinks = [
        {
          source: 'threat-intelligence',
          target: 'processor',
          active: sourceAActive,
        },
        { source: 'news', target: 'processor', active: sourceBActive },
        { source: 'playbook', target: 'processor', active: sourceBActive },
      ];
 
      const finalSourceLinks = sourceLinks.length > 0 ? sourceLinks : defaultLinks;
 
      return [
        ...finalSourceLinks,
        // Processor to outputs
        {
          source: 'processor',
          target: 'output-conclusive',
          active: processedActive,
        },
        {
          source: 'processor',
          target: 'output-inconclusive',
          active: processedActive,
        },
      ];
    },
    [sourceAActive, sourceBActive, processedActive, sourceTypeCounts],
  );
 
  return (
    <div className={classNames(STYLE_PRESETS.pageContainer)}>
      <div className={classNames(STYLE_PRESETS.contentContainer)}>
 
 
        {/* D3 Interactive Flow Diagram */}
        <div className="mt-0">
          <CardContent>
            <div className={`bg-background-primary rounded-lg p-6 border border-border-primary flex items-center justify-center min-h-[${Math.max(800, sourceTypeCounts.length * 150 + 400)}px]`}>
              <ThreatFlowDiagram
                nodes={flowNodes}
                links={flowLinks}
                width={1300} // Increased width for better spacing
                height={Math.max(800, sourceTypeCounts.length * 150 )} // Adjusted height based on source count
                onNodeClick={(node) => {
                  console.log('Node clicked:', node);
                }}
                className="mx-auto"
              />
            </div>
          </CardContent>
        </div>
 
        {/* Source Types Section */}
        {!loading && sourceTypeCounts.length > 0 && (
          <div className="mt-6">
            <Card>
              <CardHeader>
                <h3 className="text-lg font-semibold text-text-primary">
                  Threat Hunting Sources
                </h3>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {sourceTypeCounts.map((sourceType) => (
                    <div
                      key={sourceType.source_type}
                      className="flex justify-between items-center p-3 bg-background-secondary rounded-lg border border-border-primary"
                    >
                      <span className="text-sm text-text-primary font-medium">
                        {sourceType.source_type.charAt(0).toUpperCase() + sourceType.source_type.slice(1)}
                      </span>
                      <span className="text-sm font-mono text-cyber-matrix-400">
                        {sourceType.count}
                      </span>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        )}
 
        {/* Loading State */}
        {loading && (
          <div className="mt-6">
            <Card>
              <CardContent>
                <div className="flex items-center justify-center p-8">
                  <div className="text-text-muted">Loading threat hunting data...</div>
                </div>
              </CardContent>
            </Card>
          </div>
        )}
 
        {/* Error State */}
        {error && (
          <div className="mt-6">
            <Card>
              <CardContent>
                <div className="flex items-center justify-center p-8">
                  <div className="text-red-500">Error: {error}</div>
                </div>
              </CardContent>
            </Card>
          </div>
        )}
 
        {/* Status Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 pt-6">
          <Card>
            <CardHeader>
              <h3 className="text-lg font-semibold text-text-primary">
                Active Hunts
              </h3>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-sm text-text-muted">Running</span>
                  <span className="text-sm font-mono text-cyber-matrix-400">
                    {activeHunts.running}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-text-muted">Queued</span>
                  <span className="text-sm font-mono text-cyber-warning-400">
                    {activeHunts.queued}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-text-muted">Completed</span>
                  <span className="text-sm font-mono text-text-secondary">
                    {activeHunts.completed}
                  </span>
                </div>
              </div>
            </CardContent>
          </Card>
 
          <Card>
            <CardHeader>
              <h3 className="text-lg font-semibold text-text-primary">
                Detection Rate
              </h3>
            </CardHeader>
            <CardContent>
              <div className="text-center">
                <div className="text-3xl font-mono text-primary-400 mb-2">
                  {detectionRate.toFixed(1)}%
                </div>
                <div className="text-sm text-text-muted">Last 24 hours</div>
                <div className="mt-4 h-2 bg-background-tertiary rounded-full overflow-hidden">
                  <div
                    className="h-full bg-gradient-to-r from-primary-500 to-cyber-matrix-500 rounded-full transition-all duration-1000"
                    style={{ width: `${detectionRate}%` }}
                  />
                </div>
              </div>
            </CardContent>
          </Card>
 
          <Card>
            <CardHeader>
              <h3 className="text-lg font-semibold text-text-primary">
                System Health
              </h3>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className="flex justify-between items-center">
                  <span className="text-sm text-text-muted">
                    Sources Online
                  </span>
                  <div className="flex items-center space-x-2">
                    <div className="w-2 h-2 bg-cyber-matrix-400 rounded-full animate-pulse" />
                    <span className="text-sm font-mono text-cyber-matrix-400">
                      2/2
                    </span>
                  </div>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-text-muted">
                    Processing Load
                  </span>
                  <span className="text-sm font-mono text-text-primary">
                    {Math.round(systemHealth.processingLoad)}%
                  </span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-text-muted">Memory Usage</span>
                  <span className="text-sm font-mono text-text-primary">
                    {Math.round(systemHealth.memoryUsage)}%
                  </span>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
 
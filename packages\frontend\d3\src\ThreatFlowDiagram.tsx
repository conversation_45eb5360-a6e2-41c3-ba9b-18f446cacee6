import React, { useEffect, useRef } from 'react';
import * as d3 from 'd3';
 
export interface FlowNode {
  id: string;
  label: string;
  description: string;
  type: 'source' | 'processor' | 'output';
  source_type?: string;
  x?: number;
  y?: number;
  metrics?: {
    label: string;
    value: string | number;
    status?: 'good' | 'warning' | 'danger';
  }[];
  active?: boolean;
}
 
export interface FlowLink {
  source: string;
  target: string;
  active?: boolean;
  value?: number;
}
 
export interface ThreatFlowDiagramProps {
  nodes: FlowNode[];
  links: FlowLink[];
  width?: number;
  height?: number;
  className?: string;
  onNodeClick?: (node: FlowNode) => void;
}
 
export const ThreatFlowDiagram: React.FC<ThreatFlowDiagramProps> = ({
  nodes,
  links,
  width = 900,
  height,
  className = '',
  onNodeClick,
}) => {
  const svgRef = useRef<SVGSVGElement>(null);
 
  // Get unique source types and group source nodes by source_type
  const sourceNodes = nodes.filter((n) => n.type === 'source');
  const outputNodes = nodes.filter((n) => n.type === 'output');
 
  // Group source nodes by source_type
  const sourceGroups = sourceNodes.reduce((groups, node) => {
    const sourceType = node.source_type || 'Unknown';
    if (!groups[sourceType]) {
      groups[sourceType] = [];
    }
    groups[sourceType].push(node);
    return groups;
  }, {} as Record<string, FlowNode[]>);
 
  const uniqueSourceTypes = Object.keys(sourceGroups);
  const groupCount = uniqueSourceTypes.length;
 
 
  // Calculate layout parameters
  const minHeight = 500;
  const nodeSpacing = 90;
  const topPadding = 80;
  const bottomPadding = 80;
  const totalSourceNodes = sourceNodes.length;
  const totalOutputNodes = outputNodes.length;
  const maxNodes = Math.max(totalSourceNodes, totalOutputNodes, 1);
  const dynamicHeight = Math.max(minHeight, (maxNodes - 1) * nodeSpacing + topPadding + bottomPadding);
  const dynamicWidth = Math.max(width, 900);
  const actualHeight = height || dynamicHeight;
 
  // Helper to distribute nodes vertically
  const getNodeY = (index: number, total: number, height: number) => {
    if (total === 1) return height / 2;
    return topPadding + index * ((height - topPadding - bottomPadding) / (total - 1));
  };
 
  useEffect(() => {
    if (!svgRef.current) return;
 
    const svg = d3.select(svgRef.current);
    svg.selectAll('*').remove();
 
    // Create main group and establish proper z-order
    const g = svg.append('g');
 
    // Create layers in correct z-order (background to foreground)
    const backgroundLayer = g.append('g').attr('class', 'background-layer');
    const linksLayer = g.append('g').attr('class', 'links-layer');
    const animationsLayer = g.append('g').attr('class', 'animations-layer');
    const nodesLayer = g.append('g').attr('class', 'nodes-layer');
 
    // Define gradients and patterns for flow animation
    const defs = svg.append('defs');
 
    // Gradient for active flow
    const gradient = defs
      .append('linearGradient')
      .attr('id', 'flow-gradient')
      .attr('gradientUnits', 'objectBoundingBox');
 
    gradient
      .append('stop')
      .attr('offset', '0%')
      .attr('stop-color', '#1aa1dc')
      .attr('stop-opacity', 0);
 
    gradient
      .append('stop')
      .attr('offset', '50%')
      .attr('stop-color', '#1aa1dc')
      .attr('stop-opacity', 1);
 
    gradient
      .append('stop')
      .attr('offset', '100%')
      .attr('stop-color', '#1aa1dc')
      .attr('stop-opacity', 0);
 
    // Animated dashed pattern for flow lines
    const dashPattern = defs
      .append('pattern')
      .attr('id', 'dash-pattern')
      .attr('patternUnits', 'userSpaceOnUse')
      .attr('width', 20)
      .attr('height', 4);
 
    dashPattern
      .append('rect')
      .attr('width', 10)
      .attr('height', 4)
      .attr('fill', '#1aa1dc')
      .attr('opacity', 0.6);
 
    dashPattern
      .append('rect')
      .attr('x', 10)
      .attr('width', 10)
      .attr('height', 4)
      .attr('fill', 'transparent');
 
    // Glow filter for active nodes
    const filter = defs
      .append('filter')
      .attr('id', 'glow')
      .attr('x', '-50%')
      .attr('y', '-50%')
      .attr('width', '200%')
      .attr('height', '200%');
 
    filter
      .append('feGaussianBlur')
      .attr('stdDeviation', '4')
      .attr('result', 'coloredBlur');
 
    const feMerge = filter.append('feMerge');
    feMerge.append('feMergeNode').attr('in', 'coloredBlur');
    feMerge.append('feMergeNode').attr('in', 'SourceGraphic');
 
    // Position nodes: sources (left), processor (center), outputs (right)
    const nodeMap = new Map<string, FlowNode>(
      nodes.map((node, i) => {
        let x, y;
        if (node.type === 'source') {
          x = Math.max(350, dynamicWidth * 0.18);
          const index = sourceNodes.indexOf(node);
          y = getNodeY(index, totalSourceNodes, actualHeight);
        } else if (node.type === 'processor') {
          x = dynamicWidth * 0.5;
          y = actualHeight * 0.5;
        } else if (node.type === 'output') {
          x = dynamicWidth * 0.82;
          const index = outputNodes.indexOf(node);
          y = getNodeY(index, totalOutputNodes, actualHeight);
        }
        return [
          node.id,
          {
            ...node,
            x: node.x || x,
            y: node.y || y,
          },
        ];
      })
    );
 
    // Draw links with animated flow capability in links layer
    const linkElements = linksLayer
      .selectAll('.link')
      .data(links)
      .enter()
      .append('line')
      .attr('class', 'link')
      .attr('x1', (d) => nodeMap.get(d.source)!.x!)
      .attr('y1', (d) => nodeMap.get(d.source)!.y!)
      .attr('x2', (d) => nodeMap.get(d.target)!.x!)
      .attr('y2', (d) => nodeMap.get(d.target)!.y!)
      .attr('stroke', '#374151')
      .attr('stroke-width', 2)
      .attr('opacity', 0.6);
 
    // Add animated flow lines for active connections
    const flowLines = linksLayer
      .selectAll('.flow-line')
      .data(links.filter((d) => d.active))
      .enter()
      .append('line')
      .attr('class', 'flow-line')
      .attr('x1', (d) => nodeMap.get(d.source)!.x!)
      .attr('y1', (d) => nodeMap.get(d.source)!.y!)
      .attr('x2', (d) => nodeMap.get(d.target)!.x!)
      .attr('y2', (d) => nodeMap.get(d.target)!.y!)
      .attr('stroke', 'url(#dash-pattern)')
      .attr('stroke-width', 4)
      .attr('opacity', 0.8)
      .attr('stroke-dasharray', '10,5')
      .attr('stroke-dashoffset', 0);
 
    // Animate the dashed lines
    flowLines
      .transition()
      .duration(2000)
      .ease(d3.easeLinear)
      .attr('stroke-dashoffset', -30)
      .on('end', function () {
        d3.select(this).attr('stroke-dashoffset', 0);
      });
 
    // Flow animation particles - enhanced for better visibility
    const createFlowParticle = (link: FlowLink) => {
      const sourceNode = nodeMap.get(link.source)!;
      const targetNode = nodeMap.get(link.target)!;
 
      // Create a glowing particle in animations layer
      const particle = animationsLayer
        .append('circle')
        .attr('class', 'flow-particle')
        .attr('r', 6)
        .attr('fill', '#1aa1dc')
        .attr('filter', 'url(#glow)')
        .attr('cx', sourceNode.x!)
        .attr('cy', sourceNode.y!)
        .attr('opacity', 0);
 
      // Fade in and move
      particle
        .transition()
        .duration(200)
        .attr('opacity', 1)
        .transition()
        .duration(1200)
        .ease(d3.easeLinear)
        .attr('cx', targetNode.x!)
        .attr('cy', targetNode.y!)
        .transition()
        .duration(200)
        .attr('opacity', 0)
        .on('end', () => particle.remove());
    };
 
    // Create continuous flow animation
    const createContinuousFlow = (link: FlowLink) => {
      const sourceNode = nodeMap.get(link.source)!;
      const targetNode = nodeMap.get(link.target)!;
 
      // Create flowing line segments in animations layer
      const flowLine = animationsLayer
        .append('line')
        .attr('class', 'flow-line')
        .attr('x1', sourceNode.x!)
        .attr('y1', sourceNode.y!)
        .attr('x2', sourceNode.x!)
        .attr('y2', sourceNode.y!)
        .attr('stroke', '#1aa1dc')
        .attr('stroke-width', 4)
        .attr('opacity', 0.8)
        .attr('filter', 'url(#glow)');
 
      // Animate the line growing from source to target
      flowLine
        .transition()
        .duration(800)
        .ease(d3.easeQuadOut)
        .attr('x2', targetNode.x!)
        .attr('y2', targetNode.y!)
        .transition()
        .duration(400)
        .attr('opacity', 0)
        .on('end', () => flowLine.remove());
    };
 
    // Enhanced particle stream for active links
    const createParticleStream = (link: FlowLink) => {
      const sourceNode = nodeMap.get(link.source)!;
      const targetNode = nodeMap.get(link.target)!;
 
      // Create multiple particles with staggered timing
      for (let i = 0; i < 5; i++) {
        setTimeout(() => {
          if (!nodeMap.get(link.source)?.active) return; // Stop if no longer active
 
          const particle = animationsLayer
            .append('circle')
            .attr('class', 'stream-particle')
            .attr('r', 4)
            .attr('fill', '#1aa1dc')
            .attr('cx', sourceNode.x!)
            .attr('cy', sourceNode.y!)
            .attr('opacity', 0.7);
 
          particle
            .transition()
            .duration(1000)
            .ease(d3.easeLinear)
            .attr('cx', targetNode.x!)
            .attr('cy', targetNode.y!)
            .attr('opacity', 0)
            .on('end', () => particle.remove());
        }, i * 200);
      }
    };
 
    // Draw nodes in nodes layer (top layer for visibility)
    const nodeElements = nodesLayer
      .selectAll('.node')
      .data(Array.from(nodeMap.values()))
      .enter()
      .append('g')
      .attr('class', 'node')
      .attr('transform', (d) => `translate(${d.x},${d.y})`)
      .style('cursor', 'pointer')
      .on('click', (_, d) => {
        onNodeClick?.(d);
      });
 
    // Node backgrounds with different shapes for different types
    nodeElements.each(function (d) {
      const nodeEl = d3.select(this);
      const nodeData = d as FlowNode;
 
      if (nodeData.type === 'processor') {
        // Diamond background for processor
        const diamondSize = 50;
        const diamondPoints = [
          [0, -diamondSize], // top
          [diamondSize, 0], // right
          [0, diamondSize], // bottom
          [-diamondSize, 0], // left
        ]
          .map((p) => p.join(','))
          .join(' ');
 
        nodeEl
          .append('polygon')
          .attr('class', 'node-bg')
          .attr('points', diamondPoints)
          .attr('fill', '#0f172a')
          .attr('stroke', nodeData.active ? '#1aa1dc' : '#374151')
          .attr('stroke-width', nodeData.active ? 3 : 2)
          .attr('filter', nodeData.active ? 'url(#glow)' : 'none');
 
        // Add company logo in the center
        nodeEl
          .append('image')
          .attr('class', 'processor-logo')
          .attr('href', '/logos/t-logo.png')
          .attr('x', -25)
          .attr('y', -25)
          .attr('width', 50)
          .attr('height', 50)
          .attr('preserveAspectRatio', 'xMidYMid meet');
      } else if (nodeData.type === 'source') {
        // Larger rounded rectangle for sources
        // Main node background
        nodeEl
          .append('rect')
          .attr('class', 'node-bg')
          .attr('x', -60)
          .attr('y', -25)
          .attr('width', 120)
          .attr('height', 50)
          .attr('rx', 6)
          .attr('fill', '#10b981')
          .attr('stroke', nodeData.active ? '#1aa1dc' : '#374151')
          .attr('stroke-width', nodeData.active ? 2 : 1)
          .attr('filter', nodeData.active ? 'url(#glow)' : 'none');
      } else {
        // Even larger rectangle for outputs
        nodeEl
          .append('rect')
          .attr('class', 'node-bg')
          .attr('x', -70)
          .attr('y', -50)
          .attr('width', 140)
          .attr('height', 100)
          .attr('rx', 8)
          .attr(
            'fill',
            nodeData.id.includes('conclusive') &&
              !nodeData.id.includes('inconclusive')
              ? '#dc2626'
              : '#10b981',
          )
          .attr('stroke', nodeData.active ? '#1aa1dc' : '#374151')
          .attr('stroke-width', nodeData.active ? 2 : 1)
          .attr('filter', nodeData.active ? 'url(#glow)' : 'none');
      }
    });
 
    // Node status indicators (only for outputs) - adjusted for larger nodes
    nodeElements
      .filter((d: unknown) => (d as FlowNode).type === 'output')
      .append('circle')
      .attr('class', 'status-indicator')
      .attr('cx', -55)
      .attr('cy', -40)
      .attr('r', 4)
      .attr('fill', (d: unknown) =>
        (d as FlowNode).active ? '#10b981' : '#6b7280',
      );
 
    // Node labels - adjusted for larger nodes (skip processor as it has logo)
    nodeElements
      .filter((d: unknown) => (d as FlowNode).type !== 'processor')
      .append('text')
      .attr('class', 'node-label')
      .attr('text-anchor', 'middle')
      .attr('y', (d: unknown) => ((d as FlowNode).type === 'source' ? -2 : -8))
      .attr('fill', '#f8fafc')
      .attr('font-size', (d: unknown) =>
        (d as FlowNode).type === 'source' ? '12px' : '14px',
      )
      .attr('font-weight', 'bold')
      .text((d: unknown) => (d as FlowNode).label);
 
    // Output numbers for output nodes
    nodeElements
      .filter((d: unknown) => (d as FlowNode).type === 'output')
      .each(function (d) {
        const nodeEl = d3.select(this);
        const nodeData = d as FlowNode;
        const count = nodeData.description || '0';
 
        nodeEl
          .append('text')
          .attr('class', 'output-count')
          .attr('text-anchor', 'middle')
          .attr('y', 35)
          .attr('fill', '#ffffff')
          .attr('font-size', '28px')
          .attr('font-weight', 'bold')
          .text(nodeData.type === 'output' ? (nodeData.active ? count : '0') : '');
 
        nodeEl
          .append('text')
          .attr('class', 'output-detail')
          .attr('text-anchor', 'end')
          .attr('x', 60)
          .attr('y', -35)
          .attr('fill', '#cbd5e1')
          .attr('font-size', '10px')
          .text('View Detail');
      });
 
    // Add pulsing effect to active processor
    nodeElements
      .filter((d: unknown) => {
        const node = d as FlowNode;
        return node.type === 'processor' && node.active === true;
      })
      .each(function () {
        const processorNode = d3.select(this);
 
        // Add pulsing ring around processor - adjusted for diamond shape
        const pulseSize = 55;
        const pulseDiamondPoints = [
          [0, -pulseSize], // top
          [pulseSize, 0], // right
          [0, pulseSize], // bottom
          [-pulseSize, 0], // left
        ]
          .map((p) => p.join(','))
          .join(' ');
 
        processorNode
          .append('polygon')
          .attr('class', 'processor-pulse')
          .attr('points', pulseDiamondPoints)
          .attr('fill', 'none')
          .attr('stroke', '#1aa1dc')
          .attr('stroke-width', 2)
          .attr('opacity', 0)
          .transition()
          .duration(1000)
          .ease(d3.easeSinInOut)
          .attr('opacity', 0.8)
          .attr('transform', 'scale(1.2)')
          .transition()
          .duration(1000)
          .attr('opacity', 0)
          .attr('transform', 'scale(1.5)')
          .on('end', function () {
            d3.select(this).remove();
          });
      });
 
    // Node metrics (for active nodes) - adjusted for larger nodes
    nodeElements.each(function (d) {
      if (d.active && d.metrics) {
        const nodeEl = d3.select(this);
 
        if (d.type === 'source') {
          // For source nodes, display metrics more compactly
          d.metrics.slice(0, 3).forEach((metric, i) => {
            nodeEl
              .append('text')
              .attr('text-anchor', 'middle')
              .attr('y', 8 + i * 10)
              .attr(
                'fill',
                metric.status === 'good'
                  ? '#10b981'
                  : metric.status === 'warning'
                    ? '#f59e0b'
                    : metric.status === 'danger'
                      ? '#ef4444'
                      : '#94a3b8',
              )
              .attr('font-size', '8px')
              .text(`${metric.label}: ${metric.value}`);
          });
        } else if (d.type === 'output') {
          // For output nodes only (skip processor)
          d.metrics.slice(0, 2).forEach((metric, i) => {
            nodeEl
              .append('text')
              .attr('text-anchor', 'middle')
              .attr('y', 25 + i * 12)
              .attr(
                'fill',
                metric.status === 'good'
                  ? '#10b981'
                  : metric.status === 'warning'
                    ? '#f59e0b'
                    : metric.status === 'danger'
                      ? '#ef4444'
                      : '#94a3b8',
              )
              .attr('font-size', '10px')
              .text(`${metric.label}: ${metric.value}`);
          });
        }
      }
    });
 
    // Animate flows when nodes become active
    const animateFlow = () => {
      links.forEach((link) => {
        if (
          nodeMap.get(link.source)?.active ||
          nodeMap.get(link.target)?.active
        ) {
          // Create initial flow line animation
          createContinuousFlow(link);
 
          // Create particle stream for ongoing animation
          createParticleStream(link);
 
          // Create individual particles with staggered timing
          for (let i = 0; i < 3; i++) {
            setTimeout(() => createFlowParticle(link), i * 400);
          }
 
          // Animate the link itself
          linkElements
            .filter((d) => d.source === link.source && d.target === link.target)
            .transition()
            .duration(500)
            .attr('stroke', '#1aa1dc')
            .attr('stroke-width', 3)
            .attr('opacity', 1)
            .transition()
            .delay(2000)
            .duration(500)
            .attr('stroke', '#374151')
            .attr('stroke-width', 2)
            .attr('opacity', 0.6);
        }
      });
    };
 
    // Continuous animation loop for active flows
    const startContinuousAnimation = () => {
      const activeLinks = links.filter(
        (link) =>
          nodeMap.get(link.source)?.active && nodeMap.get(link.target)?.active,
      );
 
      if (activeLinks.length > 0) {
        activeLinks.forEach((link) => {
          // Create a continuous stream of particles
          const animateParticles = () => {
            if (
              nodeMap.get(link.source)?.active &&
              nodeMap.get(link.target)?.active
            ) {
              createFlowParticle(link);
              setTimeout(animateParticles, 600);
            }
          };
          animateParticles();
        });
      }
    };
 
    // Check for active flows and start animations
    const hasActiveNodes = Array.from(nodeMap.values()).some(
      (node) => node.active,
    );
    if (hasActiveNodes) {
      animateFlow();
      startContinuousAnimation();
    }
 
    // Update node appearances based on active state
    nodeElements
      .selectAll('.node-bg')
      .transition()
      .duration(300)
      .attr('stroke', (d: unknown) =>
        (d as FlowNode).active ? '#1aa1dc' : '#374151',
      )
      .attr('stroke-width', (d: unknown) => ((d as FlowNode).active ? 2 : 1))
      .attr('filter', (d: unknown) =>
        (d as FlowNode).active ? 'url(#glow)' : 'none',
      );
 
    nodeElements
      .selectAll('.status-indicator')
      .transition()
      .duration(300)
      .attr('fill', (d: unknown) =>
        (d as FlowNode).active ? '#10b981' : '#6b7280',
      );
 
    
    nodeElements.each(function (d) {
      const nodeEl = d3.select(this);
      const nodeData = d as FlowNode;

      if (nodeData.type === 'source') {

        nodeEl
          .append('text')
          .attr('class', 'source-label')
          .attr('x', -100)
          .attr('y', 0)
          .attr('fill', '#f8fafc')
          .attr('font-size', '14px')
          .attr('font-weight', 'bold')
          .attr('text-anchor', 'end')
          .text(`>> ${nodeData.label}`);

        nodeEl
          .append('text')
          .attr('class', 'source-count')
          .attr('x', -100)
          .attr('y', 18)
          .attr('fill', '#94a3b8')
          .attr('font-size', '13px')
          .attr('font-weight', 'bold')
          .attr('text-anchor', 'end')
          .text(nodeData.description);
      }
    });
  }, [nodes, links, width, actualHeight, onNodeClick]);
 
  return (
    <div className={className}>
      <svg
        ref={svgRef}
        width={dynamicWidth}
        height={actualHeight}
        style={{
          display: 'block',
          background: 'transparent',
          minHeight: `${actualHeight}px` }}
      />
    </div>
  );
};
 
import { Router, Request, Response, Router as ExpressRouter } from 'express';
import fetch from 'node-fetch';

const threatHuntsRouter: ExpressRouter = Router();

// GET /api/v1/threat_hunts
threatHuntsRouter.get('/threat_hunts', (req: Request, res: Response) => {
  (async () => {
    try {
      const apiUrl = process.env.EXTERNAL_THREAT_HUNTS_API;
      if (!apiUrl) {
        res.status(500).json({ error: 'EXTERNAL_THREAT_HUNTS_API env variable not set' });
        return;
      }
      const apiRes = await fetch(apiUrl);
      if (!apiRes.ok) {
        res.status(apiRes.status).json({ error: `Backend responded with status ${apiRes.status}` });
        return;
      }
      const data = await apiRes.json();
      res.json(data);
    } catch (err) {
      console.error('Error fetching threat hunts:', err);
      res.status(500).json({ error: 'Failed to fetch threat hunts', details: err instanceof Error ? err.message : err });
    }
  })();
});

export default threatHuntsRouter;

/**
 * Common CSS class combinations for consistent styling
 */
export const STYLE_PRESETS = {
  // Card styles
  cardPrimary: 'bg-surface-primary border-border-primary elevation-low',
  cardSecondary:
    'bg-surface-secondary border-border-secondary elevation-medium',
  cardGlass: 'glass-effect border-border-primary elevation-medium',
  cardGlassHigh: 'glass-effect border-border-primary elevation-high',

  // Status item styles
  statusItem:
    'flex items-center justify-between p-3 glass-effect rounded-lg border border-border-muted',

  // Metric styles
  metricCard:
    'glass-effect border border-border-primary rounded-lg p-4 elevation-low',
  miniMetric:
    'text-center p-3 bg-surface-secondary rounded-lg border border-border-muted',

  // Layout styles
  pageContainer: 'min-h-screen bg-background-primary p-8 pt-24',
  contentContainer: 'mx-auto max-w-[1520px]',

  // Form styles
  formInput:
    'w-full px-3 py-2 bg-background-secondary border border-border-primary rounded-md text-text-primary focus:outline-none focus:ring-2 focus:ring-primary-400',
  formSelect:
    'w-full px-3 py-2 bg-background-secondary border border-border-primary rounded-md text-text-primary focus:outline-none focus:ring-2 focus:ring-primary-400',
  formLabel: 'block text-sm font-medium text-text-primary mb-2',
  formHelpText: 'text-xs text-text-subtle mt-1',

  // Toggle/Switch styles
  toggleActive: 'bg-primary-500',
  toggleInactive: 'bg-gray-600',
  toggleThumb:
    'inline-block h-4 w-4 transform rounded-full bg-white transition-transform',
  toggleThumbActive: 'translate-x-6',
  toggleThumbInactive: 'translate-x-1',
} as const;

/**
 * Utility function to combine CSS classes with conditional logic
 */
export function classNames(
  ...classes: (string | undefined | null | false)[]
): string {
  return classes.filter(Boolean).join(' ');
}

/**
 * Utility to get toggle switch classes
 */
export function getToggleClasses(isActive: boolean) {
  return {
    container: `relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
      isActive ? STYLE_PRESETS.toggleActive : STYLE_PRESETS.toggleInactive
    }`,
    thumb: `${STYLE_PRESETS.toggleThumb} ${
      isActive
        ? STYLE_PRESETS.toggleThumbActive
        : STYLE_PRESETS.toggleThumbInactive
    }`,
  };
}

/**
 * Utility to get status badge variant classes
 */
export function getStatusBadgeVariant(
  status: string,
): 'success' | 'warning' | 'danger' | 'info' | 'secondary' {
  const normalized = status.toLowerCase();

  switch (normalized) {
    case 'active':
    case 'enabled':
    case 'success':
    case 'online':
    case 'connected':
    case 'healthy':
      return 'success';

    case 'warning':
    case 'pending':
    case 'degraded':
      return 'warning';

    case 'error':
    case 'failed':
    case 'critical':
    case 'offline':
    case 'disconnected':
    case 'unhealthy':
      return 'danger';

    case 'info':
    case 'processing':
    case 'loading':
      return 'info';

    default:
      return 'secondary';
  }
}

/**
 * Utility for responsive grid classes
 */
export function getResponsiveGrid(
  cols: { sm?: number; md?: number; lg?: number; xl?: number } = {},
): string {
  const { sm = 1, md = 2, lg = 3, xl = 4 } = cols;

  const classes = ['grid'];

  classes.push(`grid-cols-${sm}`);
  if (md !== sm) classes.push(`md:grid-cols-${md}`);
  if (lg !== md) classes.push(`lg:grid-cols-${lg}`);
  if (xl !== lg) classes.push(`xl:grid-cols-${xl}`);

  return classes.join(' ');
}

/**
 * Utility for consistent spacing
 */
export const SPACING = {
  xs: '0.5rem', // 8px
  sm: '0.75rem', // 12px
  md: '1rem', // 16px
  lg: '1.5rem', // 24px
  xl: '2rem', // 32px
  '2xl': '3rem', // 48px
  '3xl': '4rem', // 64px
} as const;

/**
 * Utility for consistent border radius
 */
export const BORDER_RADIUS = {
  none: '0',
  sm: '0.125rem',
  md: '0.375rem',
  lg: '0.5rem',
  xl: '0.75rem',
  '2xl': '1rem',
  full: '9999px',
} as const;

/**
 * Generate loading skeleton classes
 */
export function getSkeletonClasses(
  variant: 'text' | 'rectangular' | 'circular' = 'rectangular',
  size?: 'sm' | 'md' | 'lg',
): string {
  const base = 'animate-pulse bg-gray-300 dark:bg-gray-700';

  switch (variant) {
    case 'text':
      const textSize = size === 'sm' ? 'h-3' : size === 'lg' ? 'h-6' : 'h-4';
      return `${base} ${textSize} rounded`;

    case 'circular':
      const circularSize =
        size === 'sm' ? 'w-8 h-8' : size === 'lg' ? 'w-16 h-16' : 'w-12 h-12';
      return `${base} ${circularSize} rounded-full`;

    case 'rectangular':
    default:
      const rectSize = size === 'sm' ? 'h-20' : size === 'lg' ? 'h-64' : 'h-32';
      return `${base} ${rectSize} rounded`;
  }
}

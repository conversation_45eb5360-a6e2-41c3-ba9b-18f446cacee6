 
export interface User {
  id: string;
  email: string;
  name: string;
  role: 'admin' | 'user';
  createdAt: Date;
  updatedAt: Date;
}
 
export interface ApiResponse<T = unknown> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
}
 
export interface PaginationParams {
  page: number;
  limit: number;
}
 
export interface ThreatIncident {
  incident_type: 'beaconing' | 'spike' | 'anomaly' | 'ddos' | 'outlier' | 'dga';
  risk_severity: 'info' | 'unknown' | 'low' | 'medium' | 'high' | 'critical';
  investigation_status:
  | 'created'
  | 'running'
  | 'complete'
  | 'failed'
  | 'unknown';
  investigation_outcome?: 'closed' | 'automation triggered' | 'escalated' | 'action taken';
  time: number;
  uid: string;
  summary?: string;
  incident_description?: string;
  remediation_actions?: string;
  risk_message?: string;
  current_ttps?: string[];
  future_ttps?: string[];
}
 
export interface ThreatsResponse {
  data: {
    incidents: ThreatIncident[];
  };
  source: 'api' | 'cache';
  timestamp: string;
}
 
export interface Deployment {
  data: { [timestamp: string]: number };
  name: string;
  namespace: string;
  display_name?: string;
}
 
export interface DeploymentsResponse {
  data: {
    deployments: Deployment[];
  };
  source: 'api' | 'cache' | 'websocket';
  timestamp: string;
}
 
export interface ThreatHunt {
  hunt_id: string;
  title: string;
  hypothesis: string;
  curr_ttps: string;
  pred_ttps: string;
  iocs: string;
  key: string;
  query: string;
  actions: string;
  filter_threats: string;
  html_results: string;
  text: string;
  hunt_status: 'running' | 'complete' | 'failed' | 'queued';
  result_found: number;
  error_message: string | null;
  extra_research: number;
  research_sources: string;
  source: string;
  source_type: string;
  time: number;
}
 
export interface ThreatHuntsResponse {
  threat_hunts: ThreatHunt[];
}
 
export interface SourceTypeCount {
  source_type: string;
  count: number;
}
import React, { useEffect, useRef } from 'react';
import * as d3 from 'd3';

export interface HorizontalBarChartDataPoint {
    label: string;
    value: number;
}

export interface HorizontalBarChartProps {
    data: HorizontalBarChartDataPoint[];
    width?: number;
    height?: number;
    margin?: { top: number; right: number; bottom: number; left: number };
    className?: string;
    color?: string;
    colors?: string[];
    showValues?: boolean;
}

export const HorizontalBarChart: React.FC<HorizontalBarChartProps> = ({
    data,
    width = 400,
    height = 300,
    margin = { top: 20, right: 30, bottom: 40, left: 90 },
    className = '',
    color = '#3b82f6',
    colors,
    showValues = true,
}) => {
    const svgRef = useRef<SVGSVGElement>(null);

    useEffect(() => {
        if (!svgRef.current || !data.length) return;

        const svg = d3.select(svgRef.current);
        svg.selectAll('*').remove();

        const innerWidth = width - margin.left - margin.right;
        const innerHeight = height - margin.top - margin.bottom;

        const maxValue = d3.max(data, d => d.value) || 0;
        const xScale = d3
            .scaleLinear()
            .domain([0, maxValue])
            .range([0, innerWidth]);

        const yScale = d3
            .scaleBand()
            .domain(data.map(d => d.label))
            .range([0, innerHeight])
            .padding(0.1);

        
        const g = svg
            .append('g')
            .attr('transform', `translate(${margin.left}, ${margin.top})`);

        
        g.selectAll('.bar')
            .data(data)
            .enter()
            .append('rect')
            .attr('class', 'bar')
            .attr('y', d => yScale(d.label) || 0)
            .attr('width', d => xScale(d.value))
            .attr('height', yScale.bandwidth())
            .attr('fill', (d, i) => colors && colors[i] ? colors[i] : color)
            .attr('rx', 4)
            .attr('ry', 4);

        
        if (showValues) {
            g.selectAll('.value-label')
                .data(data)
                .enter()
                .append('text')
                .attr('class', 'value-label')
                .attr('x', d => xScale(d.value) + 5)
                .attr('y', d => (yScale(d.label) || 0) + yScale.bandwidth() / 2)
                .attr('dy', '0.35em')
                .attr('fill', 'currentColor')
                .attr('font-size', '12px')
                .attr('font-weight', '600')
                .style('color', 'var(--text-primary)')

                .each(function (d) {
                    d3.select(this).text(Math.round(d.value).toString());
                });
        }

        
        g.append('g')
            .attr('class', 'y-axis')
            .call(d3.axisLeft(yScale))
            .selectAll('text')
            .attr('fill', 'currentColor')
            .attr('font-size', '12px')
            .style('color', 'var(--text-secondary)');

        
        const xAxis = d3.axisBottom(xScale)
            .tickFormat(d3.format('d'))
            .ticks(Math.min(maxValue, 5)); // Limit to max 5 ticks or the max value, whichever is smaller

        g.append('g')
            .attr('class', 'x-axis')
            .attr('transform', `translate(0, ${innerHeight})`)
            .call(xAxis)
            .selectAll('text')
            .attr('fill', 'currentColor')
            .attr('font-size', '10px')
            .style('color', 'var(--text-secondary)');

        
        g.append('g')
            .attr('class', 'grid')
            .attr('transform', `translate(0, ${innerHeight})`)
            .call(d3.axisBottom(xScale)
                .tickSize(-innerHeight)
                .tickFormat(() => '')
            )
            .selectAll('line')
            .attr('stroke', 'currentColor')
            .attr('stroke-opacity', 0.1)
            .style('color', 'var(--border-secondary)');

    }, [data, width, height, margin, color, showValues]);

    return (
        <div className={`horizontal-bar-chart ${className}`}>
            <svg
                ref={svgRef}
                width={width}
                height={height}
                style={{ overflow: 'visible' }}
            />
        </div>
    );
}; 
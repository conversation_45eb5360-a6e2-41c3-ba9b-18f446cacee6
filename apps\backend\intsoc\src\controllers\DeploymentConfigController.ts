import { Router as ExpressRouter, Request, Response } from 'express';
import fetch from 'node-fetch';
import config from '../config';

export default class DeploymentConfigController {
  private router: ExpressRouter;

  constructor(router: ExpressRouter) {
    this.router = router;
    this.initializeRoutes();
  }

  initializeRoutes(): void {
    // Get deployment destinations configuration endpoint
    // GET /deployments/config/destinations
    // Response: destinations configuration data from external service
    this.router.get(
      '/deployments/config/destinations',
      async (req: Request, res: Response): Promise<void> => {
        try {
          console.log(
            'DeploymentConfigController: Fetching deployment destinations configuration from external API',
          );

          // Build the URL for the external destinations configuration service
          const destinationsUrl = `${config.externalApi.mlConfigApiUrl}/destinations`;

          console.log(
            `DeploymentConfigController: Requesting destinations config from: ${destinationsUrl}`,
          );

          // Make the request to the external service
          const response = await fetch(destinationsUrl);

          if (!response.ok) {
            throw new Error(`External API returned status: ${response.status}`);
          }

          const destinationsData = await response.json();

          console.log(
            'DeploymentConfigController: Successfully fetched deployment destinations configuration',
          );

          res.status(200).json(destinationsData);
        } catch (error) {
          console.error(
            'DeploymentConfigController: Error fetching deployment destinations configuration:',
            error,
          );

          // Handle specific error cases
          if (error instanceof Error) {
            const isConnectivityIssue =
              error.message.includes('ECONNREFUSED') ||
              error.message.includes('Failed to connect') ||
              error.message.includes('fetch failed');

            if (isConnectivityIssue) {
              res.status(503).json({
                error: 'Service Unavailable',
                message:
                  'External destinations configuration service is temporarily unavailable',
                timestamp: new Date().toISOString(),
              });
              return;
            }

            if (error.message.includes('External API returned status:')) {
              const statusMatch = error.message.match(/status: (\d+)/);
              const status = statusMatch ? parseInt(statusMatch[1]) : 500;

              res.status(status).json({
                error: 'External API Error',
                message: `Destinations configuration service returned error: ${error.message}`,
                timestamp: new Date().toISOString(),
              });
              return;
            }
          }

          // Generic error response
          res.status(500).json({
            error: 'Internal Server Error',
            message: error instanceof Error ? error.message : 'Unknown error',
            timestamp: new Date().toISOString(),
          });
        }
      },
    );

    // Update deployment destinations configuration endpoint
    // POST /deployments/config/destinations
    // Body: {"destinations": ["IRIS"], "config": {"IRIS_API_KEY": "KEY_HERE", "IRIS_HOST": "https://************"}}
    this.router.post(
      '/deployments/config/destinations',
      async (req: Request, res: Response): Promise<void> => {
        try {
          console.log(
            'DeploymentConfigController: POST /deployments/config/destinations - Updating deployment destinations configuration',
          );
          console.log('DeploymentConfigController: Request body:', JSON.stringify(req.body, null, 2));

          // Forward the request to the external destinations configuration service
          const destinationsUrl = `${config.externalApi.mlConfigApiUrl}/destinations`;

          const response = await fetch(destinationsUrl, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify(req.body),
          });

          if (!response.ok) {
            throw new Error(`External API returned status: ${response.status}`);
          }

          const result = await response.json();

          console.log(
            'DeploymentConfigController: Successfully updated deployment destinations configuration',
          );

          res.status(200).json(result);
        } catch (error) {
          console.error(
            'DeploymentConfigController: Error updating deployment destinations configuration:',
            error,
          );

          // Handle specific error cases
          if (error instanceof Error) {
            const isConnectivityIssue =
              error.message.includes('ECONNREFUSED') ||
              error.message.includes('Failed to connect') ||
              error.message.includes('fetch failed');

            if (isConnectivityIssue) {
              res.status(503).json({
                error: 'Service Unavailable',
                message:
                  'External destinations configuration service is temporarily unavailable',
                timestamp: new Date().toISOString(),
              });
              return;
            }

            if (error.message.includes('External API returned status:')) {
              const statusMatch = error.message.match(/status: (\d+)/);
              const status = statusMatch ? parseInt(statusMatch[1]) : 500;

              res.status(status).json({
                error: 'External API Error',
                message: `Destinations configuration service returned error: ${error.message}`,
                timestamp: new Date().toISOString(),
              });
              return;
            }
          }

          // Generic error response
          res.status(500).json({
            error: 'Internal Server Error',
            message: error instanceof Error ? error.message : 'Unknown error',
            timestamp: new Date().toISOString(),
          });
        }
      },
    );

    // Get specific deployment configuration endpoint
    // GET /deployments/config?name=<name>&namespace=<namespace>
    // Response: detailed configuration data for specific deployment
    this.router.get(
      '/deployments/config',
      async (req: Request, res: Response): Promise<void> => {
        try {
          const { name, namespace } = req.query;

          if (!name || !namespace) {
            res.status(400).json({
              error: 'Bad Request',
              message: 'Both name and namespace parameters are required',
              timestamp: new Date().toISOString(),
            });
            return;
          }

          console.log(
            `DeploymentConfigController: Fetching deployment configuration for ${name}@${namespace}`,
          );

          // Build the URL for the external configuration service
          const configUrl = `${config.externalApi.mlConfigApiUrl}?name=${encodeURIComponent(name as string)}&namespace=${encodeURIComponent(namespace as string)}`;

          console.log(
            `DeploymentConfigController: Requesting config from: ${configUrl}`,
          );

          // Make the request to the external service
          const response = await fetch(configUrl);

          if (!response.ok) {
            throw new Error(`External API returned status: ${response.status}`);
          }

          const configData = await response.json();

          console.log(
            'DeploymentConfigController: Successfully fetched deployment configuration',
          );

          res.status(200).json(configData);
        } catch (error) {
          console.error(
            'DeploymentConfigController: Error fetching deployment configuration:',
            error,
          );

          // Handle specific error cases
          if (error instanceof Error) {
            const isConnectivityIssue =
              error.message.includes('ECONNREFUSED') ||
              error.message.includes('Failed to connect') ||
              error.message.includes('fetch failed');

            if (isConnectivityIssue) {
              res.status(503).json({
                error: 'Service Unavailable',
                message:
                  'External configuration service is temporarily unavailable',
                timestamp: new Date().toISOString(),
              });
              return;
            }

            if (error.message.includes('External API returned status:')) {
              const statusMatch = error.message.match(/status: (\d+)/);
              const status = statusMatch ? parseInt(statusMatch[1]) : 500;

              res.status(status).json({
                error: 'External API Error',
                message: `Configuration service returned error: ${error.message}`,
                timestamp: new Date().toISOString(),
              });
              return;
            }
          }

          // Generic error response
          res.status(500).json({
            error: 'Internal Server Error',
            message: error instanceof Error ? error.message : 'Unknown error',
            timestamp: new Date().toISOString(),
          });
        }
      },
    );
  }
}

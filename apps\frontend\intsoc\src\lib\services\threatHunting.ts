import { ThreatHunt, ThreatHuntsResponse, SourceTypeCount } from '@telesoft/types';
import { apiClient } from '../api-client';
 
/**
 * Service for managing threat hunting data
 */
export class ThreatHuntingService {
    /**
     * Get all threat hunts from the API
     */
    async getThreatHunts(): Promise<ThreatHunt[]> {
        try {
            const response = await apiClient.get<ThreatHuntsResponse>('/api/v1/threat-hunting');
            return response.threat_hunts;
        } catch (error) {
            console.error('Failed to fetch threat hunts:', error);
            throw error;
        }
    }
 
    /**
     * Get source type distribution with counts
     */
    getSourceTypeDistribution(threatHunts: ThreatHunt[]): SourceTypeCount[] {
        const sourceTypeMap = new Map<string, number>();
 
        threatHunts.forEach(hunt => {
            const sourceType = hunt.source_type || 'unknown';
            sourceTypeMap.set(sourceType, (sourceTypeMap.get(sourceType) || 0) + 1);
        });
 
        return Array.from(sourceTypeMap.entries()).map(([source_type, count]) => ({
            source_type,
            count
        }));
    }
 
    /**
     * Get threat hunts by status
     */
    getThreatHuntsByStatus(threatHunts: ThreatHunt[], status: ThreatHunt['hunt_status']): ThreatHunt[] {
        return threatHunts.filter(hunt => hunt.hunt_status === status);
    }
 
    /**
     * Get threat hunts by source type
     */
    getThreatHuntsBySourceType(threatHunts: ThreatHunt[], sourceType: string): ThreatHunt[] {
        return threatHunts.filter(hunt => hunt.source_type === sourceType);
    }
 
    /**
     * Get recent threat hunts (last 24 hours)
     */
    getRecentThreatHunts(threatHunts: ThreatHunt[], hours: number = 24): ThreatHunt[] {
        const cutoffTime = Date.now() - (hours * 60 * 60 * 1000);
        return threatHunts.filter(hunt => hunt.time >= cutoffTime);
    }
 
    /**
     * Get threat hunts with results found
     */
    getThreatHuntsWithResults(threatHunts: ThreatHunt[]): ThreatHunt[] {
        return threatHunts.filter(hunt => hunt.result_found > 0);
    }
}
 
// Singleton instance
export const threatHuntingService = new ThreatHuntingService();
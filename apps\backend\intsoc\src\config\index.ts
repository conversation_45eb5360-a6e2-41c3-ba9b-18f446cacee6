import dotenv from 'dotenv';

// Load environment variables from .env file
dotenv.config();

interface RedisConfig {
  host: string;
  port: number;
  password?: string;
  db: number;
  connectTimeout: number;
  commandTimeout: number;
}

interface ExternalApiConfig {
  threatsApiUrl: string;
  threatsWebSocketUrl: string;
  mlApiUrl: string;
  mlWebSocketUrl: string;
  mlConfigApiUrl: string;
}

interface ServerConfig {
  env: string;
  port: number;
  domain: string;
  appName: string;
}

interface Config {
  server: ServerConfig;
  redis: RedisConfig;
  externalApi: ExternalApiConfig;
}

const config: Config = {
  server: {
    env: process.env.NODE_ENV || 'development',
    port: parseInt(process.env.PORT || '4001'),
    domain: process.env.DOMAIN || 'localhost',
    appName: 'intsoc-backend',
  },
  redis: {
    host: process.env.REDIS_HOST || 'localhost',
    port: parseInt(process.env.REDIS_PORT || '6379'),
    password: process.env.REDIS_PASSWORD,
    db: parseInt(process.env.REDIS_DB || '0'),
    connectTimeout: parseInt(process.env.REDIS_CONNECT_TIMEOUT || '2000'),
    commandTimeout: parseInt(process.env.REDIS_COMMAND_TIMEOUT || '1000'),
  },
  externalApi: {
    threatsApiUrl: process.env.EXTERNAL_THREATS_API || '',
    threatsWebSocketUrl: process.env.EXTERNAL_THREATS_WS || '',
    mlApiUrl: process.env.EXTERNAL_ML_API || '',
    mlWebSocketUrl: process.env.EXTERNAL_ML_WS || '',
    mlConfigApiUrl: process.env.EXTERNAL_ML_CONFIG_API || '',
  },
};

// Validate critical configuration
function validateConfig(): void {
  const errors: string[] = [];

  if (
    !config.server.port ||
    config.server.port < 1 ||
    config.server.port > 65535
  ) {
    errors.push('PORT must be a valid port number (1-65535)');
  }

  if (!config.redis.host) {
    errors.push('REDIS_HOST is required');
  }

  if (
    !config.redis.port ||
    config.redis.port < 1 ||
    config.redis.port > 65535
  ) {
    errors.push('REDIS_PORT must be a valid port number (1-65535)');
  }

  if (!config.externalApi.threatsApiUrl) {
    errors.push(
      `EXTERNAL_THREATS_API is required. Current value: '${config.externalApi.threatsApiUrl || 'undefined'}'`,
    );
  }

  if (!config.externalApi.threatsWebSocketUrl) {
    errors.push(
      `EXTERNAL_THREATS_WS is required. Current value: '${config.externalApi.threatsWebSocketUrl || 'undefined'}'`,
    );
  }

  if (!config.externalApi.mlApiUrl) {
    errors.push(
      `EXTERNAL_ML_API is required. Current value: '${config.externalApi.mlApiUrl || 'undefined'}'`,
    );
  }

  if (!config.externalApi.mlWebSocketUrl) {
    errors.push(
      `EXTERNAL_ML_WS is required. Current value: '${config.externalApi.mlWebSocketUrl || 'undefined'}'`,
    );
  }

  if (!config.externalApi.mlConfigApiUrl) {
    errors.push(
      `EXTERNAL_ML_CONFIG_API is required. Current value: '${config.externalApi.mlConfigApiUrl || 'undefined'}'`,
    );
  }

  if (errors.length > 0) {
    console.error('Configuration validation failed:');
    errors.forEach((error) => console.error(`  - ${error}`));
    process.exit(1);
  }
}

// Validate configuration on import
validateConfig();

console.log('Configuration loaded successfully:');
console.log(`  Environment: ${config.server.env}`);
console.log(`  Server Port: ${config.server.port}`);
console.log(`  Redis: ${config.redis.host}:${config.redis.port}`);
console.log(`  External API: ${config.externalApi.threatsApiUrl}`);
console.log(`  External WebSocket: ${config.externalApi.threatsWebSocketUrl}`);
console.log(`  ML API: ${config.externalApi.mlApiUrl}`);
console.log(`  ML WebSocket: ${config.externalApi.mlWebSocketUrl}`);
console.log(`  ML Config API: ${config.externalApi.mlConfigApiUrl}`);

export default config;
